"""
事件驱动系统
"""
import threading
import time
from typing import Dict, List, Callable, Any, Optional
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict
import weakref

from .logger import get_logger

class EventType(Enum):
    """事件类型"""
    # 系统事件
    SYSTEM_STARTUP = "system_startup"
    SYSTEM_SHUTDOWN = "system_shutdown"
    SYSTEM_ERROR = "system_error"
    
    # 许可证事件
    LICENSE_VALIDATED = "license_validated"
    LICENSE_EXPIRED = "license_expired"
    LICENSE_ACTIVATION_REQUIRED = "license_activation_required"
    
    # 模型事件
    MODEL_LOADED = "model_loaded"
    MODEL_UNLOADED = "model_unloaded"
    MODEL_ERROR = "model_error"
    
    # 视频处理事件
    FRAME_PROCESSED = "frame_processed"
    VIDEO_LOADED = "video_loaded"
    VIDEO_PROCESSING_STARTED = "video_processing_started"
    VIDEO_PROCESSING_COMPLETED = "video_processing_completed"
    
    # UI事件
    UI_INITIALIZED = "ui_initialized"
    UI_PARAMETER_CHANGED = "ui_parameter_changed"
    UI_FACE_SELECTED = "ui_face_selected"
    
    # 性能事件
    PERFORMANCE_WARNING = "performance_warning"
    MEMORY_WARNING = "memory_warning"
    GPU_ERROR = "gpu_error"

@dataclass
class Event:
    """事件对象"""
    type: EventType
    data: Any = None
    timestamp: float = field(default_factory=time.time)
    source: Optional[str] = None
    priority: int = 0  # 优先级，数字越大优先级越高

class EventBus:
    """事件总线"""
    
    def __init__(self):
        self.logger = get_logger("EventBus")
        self._subscribers: Dict[EventType, List[weakref.ref]] = defaultdict(list)
        self._event_queue: List[Event] = []
        self._lock = threading.RLock()
        self._processing = False
        self._stats = {
            'events_published': 0,
            'events_processed': 0,
            'subscribers_count': 0
        }
    
    def subscribe(self, event_type: EventType, handler: Callable[[Event], None], 
                 weak_ref: bool = True) -> bool:
        """
        订阅事件
        
        Args:
            event_type: 事件类型
            handler: 事件处理函数
            weak_ref: 是否使用弱引用（默认True，避免内存泄漏）
        
        Returns:
            bool: 订阅是否成功
        """
        try:
            with self._lock:
                if weak_ref:
                    # 使用弱引用避免内存泄漏
                    handler_ref = weakref.ref(handler)
                    self._subscribers[event_type].append(handler_ref)
                else:
                    # 直接引用（需要手动取消订阅）
                    self._subscribers[event_type].append(lambda: handler)
                
                self._stats['subscribers_count'] = sum(
                    len(handlers) for handlers in self._subscribers.values()
                )
                
                self.logger.debug(f"订阅事件: {event_type.value}, 处理器: {handler.__name__}")
                return True
                
        except Exception as e:
            self.logger.error(f"订阅事件失败: {e}")
            return False
    
    def unsubscribe(self, event_type: EventType, handler: Callable[[Event], None]) -> bool:
        """
        取消订阅事件
        
        Args:
            event_type: 事件类型
            handler: 事件处理函数
        
        Returns:
            bool: 取消订阅是否成功
        """
        try:
            with self._lock:
                handlers = self._subscribers.get(event_type, [])
                
                # 查找并移除处理器
                for i, handler_ref in enumerate(handlers):
                    ref_handler = handler_ref()
                    if ref_handler is handler:
                        handlers.pop(i)
                        self.logger.debug(f"取消订阅事件: {event_type.value}")
                        return True
                
                return False
                
        except Exception as e:
            self.logger.error(f"取消订阅事件失败: {e}")
            return False
    
    def publish(self, event: Event, async_process: bool = True) -> bool:
        """
        发布事件
        
        Args:
            event: 事件对象
            async_process: 是否异步处理（默认True）
        
        Returns:
            bool: 发布是否成功
        """
        try:
            with self._lock:
                self._event_queue.append(event)
                self._stats['events_published'] += 1
                
                # 按优先级排序
                self._event_queue.sort(key=lambda e: e.priority, reverse=True)
                
                self.logger.debug(f"发布事件: {event.type.value}, 数据: {event.data}")
            
            if async_process:
                # 异步处理事件
                threading.Thread(target=self._process_events, daemon=True).start()
            else:
                # 同步处理事件
                self._process_events()
            
            return True
            
        except Exception as e:
            self.logger.error(f"发布事件失败: {e}")
            return False
    
    def publish_simple(self, event_type: EventType, data: Any = None, 
                      source: str = None, priority: int = 0) -> bool:
        """
        简化的事件发布方法
        
        Args:
            event_type: 事件类型
            data: 事件数据
            source: 事件源
            priority: 优先级
        
        Returns:
            bool: 发布是否成功
        """
        event = Event(
            type=event_type,
            data=data,
            source=source,
            priority=priority
        )
        return self.publish(event)
    
    def _process_events(self):
        """处理事件队列"""
        if self._processing:
            return
        
        self._processing = True
        
        try:
            while True:
                with self._lock:
                    if not self._event_queue:
                        break
                    event = self._event_queue.pop(0)
                
                self._handle_event(event)
                self._stats['events_processed'] += 1
                
        except Exception as e:
            self.logger.error(f"处理事件队列失败: {e}")
        finally:
            self._processing = False
    
    def _handle_event(self, event: Event):
        """处理单个事件"""
        handlers = self._subscribers.get(event.type, [])
        
        # 清理失效的弱引用
        valid_handlers = []
        
        for handler_ref in handlers:
            handler = handler_ref()
            if handler is not None:
                valid_handlers.append(handler_ref)
                try:
                    handler(event)
                except Exception as e:
                    self.logger.error(f"事件处理器执行失败: {e}")
        
        # 更新有效的处理器列表
        with self._lock:
            self._subscribers[event.type] = valid_handlers
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取事件统计信息"""
        with self._lock:
            return {
                **self._stats,
                'queue_size': len(self._event_queue),
                'event_types': list(self._subscribers.keys()),
                'is_processing': self._processing
            }
    
    def clear_queue(self):
        """清空事件队列"""
        with self._lock:
            self._event_queue.clear()
            self.logger.info("事件队列已清空")

class EventSubscriber:
    """事件订阅者基类"""
    
    def __init__(self, event_bus: EventBus):
        self.event_bus = event_bus
        self.logger = get_logger(self.__class__.__name__)
        self._subscriptions: List[EventType] = []
    
    def subscribe_to_events(self, event_types: List[EventType]):
        """批量订阅事件"""
        for event_type in event_types:
            if self.event_bus.subscribe(event_type, self.handle_event):
                self._subscriptions.append(event_type)
    
    def unsubscribe_all(self):
        """取消所有订阅"""
        for event_type in self._subscriptions:
            self.event_bus.unsubscribe(event_type, self.handle_event)
        self._subscriptions.clear()
    
    def handle_event(self, event: Event):
        """事件处理方法，子类需要重写"""
        method_name = f"on_{event.type.value}"
        if hasattr(self, method_name):
            try:
                getattr(self, method_name)(event)
            except Exception as e:
                self.logger.error(f"处理事件 {event.type.value} 失败: {e}")
        else:
            self.logger.debug(f"未找到事件处理方法: {method_name}")

# 全局事件总线
event_bus = EventBus()

# 便捷函数
def subscribe(event_type: EventType, handler: Callable[[Event], None]) -> bool:
    """订阅事件的便捷函数"""
    return event_bus.subscribe(event_type, handler)

def unsubscribe(event_type: EventType, handler: Callable[[Event], None]) -> bool:
    """取消订阅事件的便捷函数"""
    return event_bus.unsubscribe(event_type, handler)

def publish(event_type: EventType, data: Any = None, source: str = None, priority: int = 0) -> bool:
    """发布事件的便捷函数"""
    return event_bus.publish_simple(event_type, data, source, priority)

# 装饰器用于自动事件发布
def auto_publish_event(event_type: EventType, data_extractor: Callable = None):
    """自动发布事件的装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                result = func(*args, **kwargs)
                
                # 提取事件数据
                if data_extractor:
                    event_data = data_extractor(result, *args, **kwargs)
                else:
                    event_data = result
                
                # 发布事件
                publish(event_type, event_data, source=func.__name__)
                
                return result
            except Exception as e:
                # 发布错误事件
                publish(EventType.SYSTEM_ERROR, {
                    'function': func.__name__,
                    'error': str(e),
                    'args': args,
                    'kwargs': kwargs
                })
                raise
        return wrapper
    return decorator
