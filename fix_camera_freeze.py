#!/usr/bin/env python3
"""
修复摄像头换脸卡住问题
"""

import sys
import os
import datetime

def setup_environment():
    """设置环境变量"""
    os.environ['ROPE_SESSION'] = 'camera_fix_' + str(int(datetime.datetime.now().timestamp()))
    os.environ['ROPE_LAUNCH_TIME'] = datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")

def test_video_processing():
    """测试视频处理功能"""
    try:
        print("测试视频处理功能...")
        
        # 测试基础导入
        from rope.VideoManager import VideoManager
        print("✓ VideoManager导入成功")
        
        # 测试GUI
        from rope.GUI import GUI
        print("✓ GUI导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 视频处理测试失败: {e}")
        return False

def verify_fixes():
    """验证修复是否生效"""
    try:
        print("验证修复效果...")
        
        # 检查GUI.py修复
        with open("rope/GUI.py", 'r', encoding='utf-8') as f:
            gui_content = f.read()
        
        if "image is not None and hasattr(image, 'shape')" in gui_content:
            print("✓ GUI.py 安全检查已添加")
        else:
            print("❌ GUI.py 修复未生效")
            return False
        
        # 检查VideoManager.py修复
        with open("rope/VideoManager.py", 'r', encoding='utf-8') as f:
            vm_content = f.read()
        
        if "确保返回有效结果" in vm_content:
            print("✓ VideoManager.py 错误处理已改进")
        else:
            print("❌ VideoManager.py 修复未生效")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 验证修复失败: {e}")
        return False

def create_debug_config():
    """创建调试配置"""
    try:
        print("创建调试配置...")
        
        import json
        
        debug_config = {
            "debug_mode": True,
            "verbose_logging": True,
            "safe_image_processing": True,
            "error_recovery": True,
            "fallback_enabled": True,
            "camera_settings": {
                "buffer_size": 1,
                "timeout": 5000,
                "retry_count": 3,
                "safe_mode": True
            },
            "swap_settings": {
                "enable_fallback": True,
                "safe_processing": True,
                "error_tolerance": "high",
                "return_original_on_error": True
            }
        }
        
        os.makedirs("config", exist_ok=True)
        with open("config/debug.json", 'w', encoding='utf-8') as f:
            json.dump(debug_config, f, indent=2)
        
        print("✓ 调试配置已创建")
        return True
        
    except Exception as e:
        print(f"❌ 创建调试配置失败: {e}")
        return False

def create_camera_test_script():
    """创建摄像头测试脚本"""
    try:
        print("创建摄像头测试脚本...")
        
        test_script = '''#!/usr/bin/env python3
"""
摄像头换脸测试脚本
"""

import cv2
import numpy as np
import time

def test_camera():
    """测试摄像头基础功能"""
    print("测试摄像头...")
    
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("❌ 无法打开摄像头")
        return False
    
    print("✓ 摄像头打开成功")
    
    # 测试读取帧
    for i in range(10):
        ret, frame = cap.read()
        if not ret:
            print(f"❌ 读取第{i+1}帧失败")
            cap.release()
            return False
        
        print(f"✓ 第{i+1}帧读取成功 - 尺寸: {frame.shape}")
        time.sleep(0.1)
    
    cap.release()
    print("✓ 摄像头测试完成")
    return True

def test_image_processing():
    """测试图像处理"""
    print("测试图像处理...")
    
    # 创建测试图像
    test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    print(f"✓ 测试图像创建成功 - 尺寸: {test_image.shape}")
    
    # 测试基础处理
    try:
        # 颜色空间转换
        rgb_image = cv2.cvtColor(test_image, cv2.COLOR_BGR2RGB)
        print("✓ 颜色空间转换成功")
        
        # 尺寸调整
        resized = cv2.resize(rgb_image, (512, 512))
        print("✓ 图像缩放成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 图像处理失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("摄像头换脸测试")
    print("=" * 50)
    
    tests = [
        ("摄像头基础功能", test_camera),
        ("图像处理功能", test_image_processing),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 摄像头功能正常！")
        return True
    else:
        print("⚠️ 部分功能异常，请检查硬件连接。")
        return False

if __name__ == "__main__":
    success = main()
    input("按任意键退出...")
'''
        
        with open("test_camera.py", 'w', encoding='utf-8') as f:
            f.write(test_script)
        
        print("✓ 摄像头测试脚本已创建: test_camera.py")
        return True
        
    except Exception as e:
        print(f"❌ 创建测试脚本失败: {e}")
        return False

def show_troubleshooting_guide():
    """显示故障排除指南"""
    print("\n" + "="*60)
    print("🔧 摄像头换脸卡住问题 - 故障排除指南")
    print("="*60)
    
    print("\n📋 已修复的问题:")
    print("  ✓ GUI图像处理空指针错误")
    print("  ✓ VideoManager返回值检查")
    print("  ✓ 错误恢复机制")
    print("  ✓ 安全的图像处理")
    
    print("\n🎯 使用建议:")
    print("  1. 重启程序测试修复效果")
    print("  2. 确保摄像头正常工作")
    print("  3. 选择合适的源人脸图片")
    print("  4. 逐步启用换脸功能")
    
    print("\n🔍 故障排除步骤:")
    print("  1. 运行 test_camera.py 测试摄像头")
    print("  2. 检查控制台错误信息")
    print("  3. 尝试不同的摄像头分辨率")
    print("  4. 确保有足够的GPU内存")
    
    print("\n⚠️ 常见问题:")
    print("  • 摄像头被其他程序占用")
    print("  • GPU内存不足")
    print("  • 源人脸图片质量问题")
    print("  • 网络摄像头驱动问题")
    
    print("\n💡 优化建议:")
    print("  • 使用高质量的源人脸图片")
    print("  • 确保良好的光照条件")
    print("  • 关闭不必要的后台程序")
    print("  • 使用有线连接的摄像头")

def main():
    """主函数"""
    print("=" * 60)
    print("Rope Live Stellar - 摄像头换脸卡住问题修复")
    print("=" * 60)
    
    # 设置环境
    setup_environment()
    
    fixes = [
        ("测试视频处理", test_video_processing),
        ("验证修复效果", verify_fixes),
        ("创建调试配置", create_debug_config),
        ("创建测试脚本", create_camera_test_script),
    ]
    
    completed = 0
    total = len(fixes)
    
    for fix_name, fix_func in fixes:
        print(f"\n--- {fix_name} ---")
        try:
            if fix_func():
                completed += 1
                print(f"✅ {fix_name} 完成")
            else:
                print(f"⚠️ {fix_name} 部分完成")
        except Exception as e:
            print(f"❌ {fix_name} 失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"修复结果: {completed}/{total} 完成")
    
    if completed >= 2:  # 至少完成主要修复
        print("🎉 摄像头卡住问题已修复！")
        show_troubleshooting_guide()
        
        print("\n🚀 下一步:")
        print("  1. 重启 Rope Live Stellar")
        print("  2. 测试摄像头换脸功能")
        print("  3. 如果仍有问题，运行 test_camera.py")
        
        return True
    else:
        print("⚠️ 部分修复失败，请检查错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
