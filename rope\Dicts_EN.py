import os
import sys
import cv2

def get_media_path(filename):
    """获取媒体文件的绝对路径"""
    if getattr(sys, 'frozen', False):
        base_path = sys._MEIPASS
    else:
        base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    return os.path.join(base_path, 'rope', 'media', filename)

def get_model_path(filename):
    """获取模型文件的绝对路径"""
    if getattr(sys, 'frozen', False):
        exe_dir = os.path.dirname(sys.executable)
        model_path = os.path.join(exe_dir, 'models', filename)
        print(f"Executable directory: {exe_dir}")
        print(f"Full model path: {model_path}")
        return model_path
    else:
        # 开发环境的路径
        base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        return os.path.join(base_path, 'models', filename)
DEFAULT_DATA = {
# Buttons
'AddMarkerButtonDisplay':           'icon',
'AddMarkerButtonIconHover':            get_media_path('add_marker_hover.png'),
'AddMarkerButtonIconOff':              get_media_path('add_marker_off.png'),
'AddMarkerButtonIconOn':               get_media_path('add_marker_off.png'),
'AddMarkerButtonInfoText':             'ADD MARKER:\nAttaches a parameter marker to the current frame. Markers copy all parameter settings and apply them to all future frames, or until another marker is encountered.',
'AddMarkerButtonState':           False,

'SaveMarkerButtonDisplay':           'icon',
'SaveMarkerButtonIconHover':            get_media_path('marker_save.png'),
'SaveMarkerButtonIconOff':              get_media_path('marker_save.png'),
'SaveMarkerButtonIconOn':               get_media_path('marker_save.png'),
'SaveMarkerButtonInfoText':             'SAVE MARKERS:\nSave markers for this source video. The markers will be saved as a json file in the same folder as your source video.',
'SaveMarkerButtonState':           False,

'AudioDisplay':             'text',
'AudioInfoText':             'ENABLE REAL-TIME AUDIO:\nAdds audio from the input video during preview playback. If you are unable to maintain the input video frame rate, the audio will lag.',
'AudioState':               False,
'AudioText':                'Enable Audio',
'AutoSwapState':            False,
'ClearFacesDisplay':        'text',
'ClearFacesIcon':            get_media_path('tarfacedel.png'),
'ClearFacesIconHover':      get_media_path('rec.png'),
'ClearFacesIconOff':        get_media_path('rec.png'),
'ClearFacesIconOn':         get_media_path('rec.png'),
'ClearFacesInfoText':             'REMOVE FACES:\nRemove all currently found faces.',
'ClearFacesState':          False,
'ClearFacesText':           'Clear Faces',
'ClearmemState':            False,
'DefaultParamsButtonDisplay':           'text',
'DefaultParamsButtonInfoText':             'LOAD DEFAULT PARAMETERS:\nLoad the StarPluck default parameters for this column.',
'DefaultParamsButtonState':           False,
'DefaultParamsButtonText':           'Load Defaults',
'DelEmbedDisplay':          'text',
'DelEmbedIconHover':        get_media_path('rec.png'),
'DelEmbedIconOff':          get_media_path('rec.png'),
'DelEmbedIconOn':           get_media_path('rec.png'),
'DelEmbedInfoText':             'DELETE EMBEDDING:\nDelete the currently selected embedding',
'DelEmbedState':            False,
'DelEmbedText':             'Delete Emb',
'DelMarkerButtonDisplay':           'icon',
'DelMarkerButtonIconHover':            get_media_path('remove_marker_hover.png'),
'DelMarkerButtonIconOff':              get_media_path('remove_marker_off.png'),
'DelMarkerButtonIconOn':               get_media_path('remove_marker_off.png'),
'DelMarkerButtonInfoText':             'REMOVE MARKER:\nRemoves the parameter marker from the current frame.',
'DelMarkerButtonState':           False,
'FindFacesDisplay':         'text',
'FindFacesIcon':         get_media_path('tarface.png'),
'FindFacesIconHover':       get_media_path('rec.png'),
'FindFacesIconOff':         get_media_path('rec.png'),
'FindFacesIconOn':          get_media_path('rec.png'),
'FindFacesInfoText':             'FIND FACES:\nFinds all new faces in the current frame.',
'FindFacesState':           False,
'FindFacesText':            'Find Faces',
'ImgDockState':             False,
'ImgVidMode':               'Videos',
'ImgVidState':              False,
'LoadParamsButtonDisplay':           'text',
'LoadParamsButtonInfoText':             'LOAD SAVED PARAMETERS:\nLoads all parameters from this column if they have been previously saved. ',
'LoadParamsButtonState':           False,
'LoadParamsButtonText':           'Load Params',
'LoadSFacesDisplay':         'both',
'LoadSFacesIcon':            get_media_path('save.png'),
'LoadSFacesIconHover':        get_media_path('save.png'),
'LoadSFacesIconOff':          get_media_path('save.png'),
'LoadSFacesIconOn':           get_media_path('save.png'),
'LoadSFacesInfoText':             'SELECT SOURCE FACES FOLDER:\nSelects and loads Source Faces from Folder. Make sure the folder only contains <good> images.',
'LoadSFacesState':          False,
'LoadSFacesText':           'Select Faces Folder',
'LoadTVideosDisplay':         'both',
'LoadTVideosIconHover':        get_media_path('save.png'),
'LoadTVideosIconOff':          get_media_path('save.png'),
'LoadTVideosIconOn':           get_media_path('save.png'),
'LoadTVideosInfoText':             'SELECT INPUT VIDEOS/IMAGES FOLDER:\nSelect and load media from folder.',
'LoadTVideosState':         False,
'LoadTVideosText':           'Select Videos Folder',
'MaskViewDisplay':         'text',
'MaskViewInfoText':             'SHOW MASKS:\nDisplays the mask for a face side-by-side with the face. Useful for understanding the masking behaviors and results.',
'MaskViewState':            False,
'MaskViewText':           'Show Mask',
'CompareViewDisplay':         'text',
'CompareViewInfoText':             'SHOW COMPARE VIEW:\nDisplays the swapped face side-by-side with the original face.',
'CompareViewState':            False,
'CompareViewText':           'Show Compare View',
'NextMarkerButtonDisplay':           'icon',
'NextMarkerButtonIconHover':            get_media_path('next_marker_hover.png'),
'NextMarkerButtonIconOff':              get_media_path('next_marker_off.png'),
'NextMarkerButtonIconOn':               get_media_path('next_marker_off.png'),
'NextMarkerButtonInfoText':             'NEXT MARKER:\nMove to the next marker.',
'NextMarkerButtonState':           False,
'OutputFolderDisplay':         'both',
'OutputFolderIconHover':        get_media_path('save.png'),
'OutputFolderIconOff':          get_media_path('save.png'),
'OutputFolderIconOn':           get_media_path('save.png'),
'OutputFolderInfoText':             'SELECT SAVE FOLDER:\nSelect folder for saved videos and images.',
'OutputFolderState':        False,
'OutputFolderText':           'Select Output Folder',
'PerfTestState':            False,
'PlayDisplay':              'icon',
'PlayIconHover':            get_media_path('play_hover.png'),
'PlayIconOff':              get_media_path('play_off.png'),
'PlayIconOn':               get_media_path('play_on.png'),
'PlayInfoText':             'PLAY:\nPlays the video. Press again to stop playing',
'PlayState':                False,
'PrevMarkerButtonDisplay':           'icon',
'PrevMarkerButtonIconHover':            get_media_path('previous_marker_hover.png'),
'PrevMarkerButtonIconOff':              get_media_path('previous_marker_off.png'),
'PrevMarkerButtonIconOn':               get_media_path('previous_marker_off.png'),
'PrevMarkerButtonInfoText':             'PREVIOUS MARKER:\nMove to the previous marker.',
'PrevMarkerButtonState':           False,
'RecordDisplay':            'icon',
'RecordIconHover':          get_media_path('rec_hover.png'),
'RecordIconOff':            get_media_path('rec_off.png'),
'RecordIconOn':             get_media_path('rec_on.png'),
'RecordInfoText':             'RECORD:\nArms the PLAY button for recording. Press RECORD, then PLAY to record. Press PLAY again to stop recording.',
'RecordState':              False,
'SaveImageState':           False,
'SaveParamsButtonDisplay':           'text',
'SaveParamsButtonInfoText':             'SAVE PARAMETERS:\nSaves all parameters in this column.',
'SaveParamsButtonState':            False,
'SaveParamsButtonText':             'Save Params',
'StartRopeDisplay':                 'both',
'StartRopeIconHover':               get_media_path('rope.png'),
'StartRopeIconOff':                 get_media_path('rope.png'),
'StartRopeIconOn':                  get_media_path('rope.png'),
'StartRopeInfoText':                'Loading Resources:\nLoading video and image resources.',
'StartRopeState':                   False,
'StartRopeText':                    'Loading Resources',
'SwapFacesDisplay':                 'text',
'SwapFacesInfoText':                'SWAP:\nSwap assigned Source Faces and Target Faces.',
'SwapFacesState':                   False,
'SwapFacesText':                    'Swap Faces',
'EditFacesDisplay':                 'text',
'EditFacesInfoText':                'Edit:\nEdit Faces.',
'EditFacesState':                   False,
'EditFacesText':                    'Edit Faces',
'EnhanceFrameDisplay':              'text',
'EnhanceFrameInfoText':             'Enhance:\nEnhance Frame.',
'EnhanceFrameState':                False,
'EnhanceFrameText':                 'Enhance Frame',
'TLBeginningDisplay':              'icon',
'TLBeginningIconHover':            get_media_path('tl_beg_hover.png'),
'TLBeginningIconOff':              get_media_path('tl_beg_off.png'),
'TLBeginningIconOn':               get_media_path('tl_beg_on.png'),
'TLBeginningInfoText':             'TIMELINE START:\nMove the timeline handle to the first frame.',
'TLBeginningState':                 False,
'TLLeftDisplay':                    'icon',
'TLLeftIconHover':                  get_media_path('tl_left_hover.png'),
'TLLeftIconOff':                    get_media_path('tl_left_off.png'),
'TLLeftIconOn':                     get_media_path('tl_left_on.png'),
'TLLeftInfoText':                   'TIMELEFT NUDGE LEFT:\nMove the timeline handle to the left 30 frames.',
'TLLeftState':                      False,
'TLRightDisplay':                   'icon',
'TLRightIconHover':                 get_media_path('tl_right_hover.png'),
'TLRightIconOff':                   get_media_path('tl_right_off.png'),
'TLRightIconOn':                    get_media_path('tl_right_on.png'),
'TLRightInfoText':                  'TIMELEFT NUDGE RIGHT:\nMove the timeline handle to the RIGHT 30 frames.',
'TLRightState':                     False,

'SaveImageButtonDisplay':                   'text',
'SaveImageButtonInfoText':                  'SAVE IMAGE:\nSaves the current image to your Output Folder.',
'SaveImageButtonState':                     False,
'SaveImageButtonText':             'Save Image',

'AutoSwapButtonDisplay':                   'text',
'AutoSwapButtonInfoText':                  'AUTOSWAP:\nAutomatcially applies your currently selected Input Face to new images.',
'AutoSwapButtonState':                     False,
'AutoSwapButtonText':             'Auto Swap',

'ClearVramButtonDisplay':                   'text',
'ClearVramButtonInfoText':                  'CLEAR VRAM:\nClears models from your VRAM.',
'ClearVramButtonState':                     False,
'ClearVramButtonText':             'Clear VRAM',

'GetNewEmbButtonDisplay':                   'text',
'GetNewEmbButtonInfoText':                  'CLEAR VRAM:\nClears models from your VRAM.',
'GetNewEmbButtonState':                     False,
'GetNewEmbButtonText':             'Clear VRAM',

'StopMarkerButtonnDisplay':                   'icon',
'StopMarkerButtonIconHover':            get_media_path('previous_marker_hover.png'),
'StopMarkerButtonIconOff':              get_media_path('previous_marker_off.png'),
'StopMarkerButtonIconOn':               get_media_path('previous_marker_off.png'),
'StopMarkerButtonInfoText':                  'CLEAR VRAM:\nClears models from your VRAM.',
'StopMarkerButtonState':                     False,
'StopMarkerButtonText':             'Clear VRAM',

#AutoColor
'AutoColorSwitchInfoText':              'RGB ADJUSTMENT:\nFine-tune the RGB color values of the swap.',
'AutoColorSwitchState':             False,

# Final Blur Switches
'FinalBlurSwitchInfoText':        'Final Blur Switch:\nPerform blurring at the end of the pipeline.',
'FinalBlurSwitchState':           False,

# Switches
'ColorSwitchInfoText':              'RGB ADJUSTMENT:\nFine-tune the RGB color values of the swap.',
'ColorSwitchState':                 False,
'DiffSwitchInfoText':               'DIFFERENCER:\nAllow some of the original face to show in the swapped result when the difference between the two images is small. Can help bring back some texture to the swapped face',
'DiffSwitchState':                  False,
'FaceAdjSwitchInfoText':            'KPS and SCALE ADJUSTMENT:\nThis is an experimental feature to perform direct adjustments to the face landmarks found by the detector. There is also an option to adjust the scale of the swapped face.',
'FaceAdjSwitchState':               False,
# Face Landmarks Detection
'LandmarksDetectionAdjSwitchInfoText': 'KPS ADJUSTMENT:\nThis is an experimental feature to perform direct adjustments to the face landmarks found by the detector. ',
'LandmarksDetectionAdjSwitchState':    False,
'LandmarksAlignModeFromPointsSwitchInfoText': 'KPS ADJUSTMENT ALIGN MODE FROM POINTS:\nThis is an experimental feature to perform direct adjustments to the face landmarks found from detector key points.',
'LandmarksAlignModeFromPointsSwitchState':    False,
'ShowLandmarksSwitchInfoText':      'Show Landmarks in realtime.',
'ShowLandmarksSwitchState':         False,
#
# Face Landmarks Position
'LandmarksPositionAdjSwitchInfoText': '5 - KPS ADJUSTMENT:\nThis is an experimental feature to perform direct adjustments to the position of face landmarks found by the detector. ',
'LandmarksPositionAdjSwitchState':    False,
#
# Face Likeness
'FaceLikenessSwitchInfoText':       'Face Likeness:\nThis is an experimental feature to perform direct adjustments to likeness of faces.',
'FaceLikenessSwitchState':           False,
#
# Auto Rotation
'AutoRotationSwitchInfoText':       'Auto Rotation:\nAutomatically Rotate the frames to find the best detection angle',
'AutoRotationSwitchState':           False,
#
'FaceParserSwitchInfoText':         'BACKGROUND MASK:\nAllow the unprocessed background from the orginal image to show in the final swap.',
'FaceParserSwitchState':            False,
'MouthParserSwitchInfoText':        'MOUTH MASK:\nAllow the mouth from the original face to show on the swapped face.',
'MouthParserSwitchState':           False,
'OccluderSwitchInfoText':           'OCCLUSION MASK:\nAllow objects occluding the face to show up in the swapped image.',
'OccluderSwitchState':              False,
'DFLRCTColorSwitchInfoText':        'DFL RCT COLOR TRANSFER:\nUse RCT color transfer when swapping using DFL Models',
'DFLRCTColorSwitchState':           False,
'DFLLoadOnlyOneSwitchInfoText':     'DFL LOAD ONLY ONE MODEL:\nKeep only one DFL model in memory, when using DFL swapping',
'DFLLoadOnlyOneSwitchState':        False,
'DFLXSegSwitchInfoText':            'DFL XSEG MASK:\nAllow objects occluding the face to show up in the swapped image.',
'DFLXSegSwitchState':               False,
'OrientSwitchInfoText':             'ORIENTATION:\nRotate the face detector to better detect faces at different angles',
'OrientSwitchState':                False,
'RestorerSwitchInfoText':           'FACE RESTORER:\nRestore the swapped image by upscaling.',
'RestorerSwitchState':              False,
'Restorer2SwitchInfoText':           'FACE REPAIRER:\nRepair the swapped image by upscaling.',
'Restorer2SwitchState':              False,
'StrengthSwitchInfoText':           'SWAPPER STRENGTH:\nApply additional swapping iterations to increase the strength of the result, which may increase likeness',
'StrengthSwitchState':              False,
'CLIPSwitchInfoText':               'TEXT MASKING:\nUse descriptions to identify objects that will be present in the final swapped image.',
'CLIPSwitchState':                  False,

'VirtualCameraSwitchState':         False,
'VirtualCameraSwitchInfoText':      'VIRTUAL CAMERA:\nFeed the swapped video output to virtual camera for using in external applications',

'RestoreEyesSwitchInfoText':        'RESTORE EYES: \nRestore eyes from the original face',
'RestoreEyesSwitchState':           False,
'RestoreMouthSwitchInfoText':       'RESTORE MOUTH: \nRestore mouth from the original face',
'RestoreMouthSwitchState':          False,

'JpegCompressionSwitchInfoText':     'JPEG FACE COMPRESSION:\nThe effect of JPEG compression on the face.',
'JpegCompressionSwitchState':        False,
# Sliders
'BlendSliderAmount':                5,
'BlendSliderInc':                   1,
'BlendSliderInfoText':              'BLEND:\nCombined masks blending distance. Is not applied to the border masks.',
'BlendSliderMax':                   100,
'BlendSliderMin':                   0,
'FinalBlurSliderAmount':           0,
'FinalBlurSliderInc':              1,
'FinalBlurSliderInfoText':         'BORDER MASK BLEND:\nBorder mask blending distance.',
'FinalBlurSliderMax':              50,
'FinalBlurSliderMin':              0,

'BorderBlurSliderAmount':           10,
'BorderBlurSliderInc':              1,
'BorderBlurSliderInfoText':         'BORDER MASK BLEND:\nBorder mask blending distance.',
'BorderBlurSliderMax':              100,
'BorderBlurSliderMin':              0,
'BorderBottomSliderAmount':         10,
'BorderBottomSliderInc':            1,
'BorderBottomSliderInfoText':       'BOTTOM BORDER DISTANCE:\nA rectangle with adjustable top, bottom, and sides that blends the swapped face rseult back into the original image.',
'BorderBottomSliderMax':            100,
'BorderBottomSliderMin':            0,
'BorderLeftSliderAmount':           10,
'BorderLeftSliderInc':              1,
'BorderLeftSliderInfoText':         'LEFT BORDER DISTANCE:\nA rectangle with adjustable top, bottom, and sides that blends the swapped face result back into the original image.',
'BorderLeftSliderMax':              100,
'BorderLeftSliderMin':              0,
'BorderRightSliderAmount':          10,
'BorderRightSliderInc':             1,
'BorderRightSliderInfoText':        'RIGHT BORDER DISTANCE:\nA rectangle with adjustable top, bottom, and sides that blends the swapped face result back into the original image.',
'BorderRightSliderMax':             100,
'BorderRightSliderMin':             0,
'BorderTopSliderAmount':            10,
'BorderTopSliderInc':               1,
'BorderTopSliderInfoText':          'TOP BORDER DISTANCE:\nA rectangle with adjustable top, bottom, and sides that blends the swapped face result back into the original image.',
'BorderTopSliderMax':               100,
'BorderTopSliderMin':               0,
'AutoColorSliderAmount':            80,
'AutoColorSliderInc':               5,
'AutoColorSliderInfoText':          'AUTO MIX COLOR VALUES:\nFine-tune the RGB color values of the swap.',
'AutoColorSliderMax':               100,
'AutoColorSliderMin':               0,
'ColorBlueSliderAmount':            0,
'ColorBlueSliderInc':               1,
'ColorBlueSliderInfoText':          'RGB BLUE ADJUSTMENT',
'ColorBlueSliderMax':               100,
'ColorBlueSliderMin':               -100,
'ColorGreenSliderAmount':           0,
'ColorGreenSliderInc':              1,
'ColorGreenSliderInfoText':         'RGB GREEN ADJUSTMENT',
'ColorGreenSliderMax':              100,
'ColorGreenSliderMin':              -100,
'ColorRedSliderAmount':             0,
'ColorRedSliderInc':                1,
'ColorRedSliderInfoText':           'RGB RED ADJUSTMENT',
'ColorRedSliderMax':                100,
'ColorRedSliderMin':                -100,
'DetectScoreSliderAmount':          50,
'DetectScoreSliderInc':             1,
'DetectScoreSliderInfoText':        'DETECTION SCORE LIMIT:\nDetermines the minimum score required for a face to be detected. Higher values require higher quality faces. E.g., if faces are flickering when at extreme angles, raising this will limit swapping attempts.',
'DetectScoreSliderMax':             100,
'DetectScoreSliderMin':             1,
# Face Landmarks Detection
'LandmarksDetectScoreSliderAmount':  50,
'LandmarksDetectScoreSliderInc':     1,
'LandmarksDetectScoreSliderInfoText':'LANDMARKS DETECTION SCORE LIMIT:\nDetermines the minimum score required for a face to be detected. Higher values require higher quality faces. E.g., if faces are flickering when at extreme angles, raising this will limit swapping attempts.',
'LandmarksDetectScoreSliderMax':     100,
'LandmarksDetectScoreSliderMin':     1,
# Jpeg Compression
'JpegCompressionSliderAmount':       50,
'JpegCompressionSliderInc':          1,
'JpegCompressionSliderInfoText':     'REPAIR THRESHOLD:\nMix the repaired result back into the original swap.',
'JpegCompressionSliderMax':          100,
'JpegCompressionSliderMin':          1,
#
# Face Likeness
'FaceLikenessFactorSliderAmount':    0.00,
'FaceLikenessFactorSliderInc':       0.05,
'FaceLikenessFactorSliderInfoText':  'Face Likeness Factor:\nDetermines the factor of likeness between the source and assigned faces.',
'FaceLikenessFactorSliderMax':       1.00,
'FaceLikenessFactorSliderMin':       -1.00,
#
# Face Landmarks Position
'FaceIDSliderAmount':               1,
'FaceIDSliderInc':                  1,
'FaceIDSliderInfoText':             'LANDMARKS POSITION FACE ID:\nDetermines the target face for which the positions of the facial points can be modified.',
'FaceIDSliderMax':                  20,
'FaceIDSliderMin':                  1,
'EyeLeftXSliderAmount':             0,
'EyeLeftXSliderInc':                1,
'EyeLeftXSliderInfoText':           'Eye Left X-DIRECTION AMOUNT:\nShifts the eye left detection point left and right',
'EyeLeftXSliderMax':                100,
'EyeLeftXSliderMin':                -100,
'EyeLeftYSliderAmount':             0,
'EyeLeftYSliderInc':                1,
'EyeLeftYSliderInfoText':           'Eye Left Y-DIRECTION AMOUNT:\nShifts the eye left detection point up and down',
'EyeLeftYSliderMax':                100,
'EyeLeftYSliderMin':                -100,
'EyeRightXSliderAmount':             0,
'EyeRightXSliderInc':                1,
'EyeRightXSliderInfoText':           'Eye Left X-DIRECTION AMOUNT:\nShifts the eye right detection point left and right',
'EyeRightXSliderMax':                100,
'EyeRightXSliderMin':                -100,
'EyeRightYSliderAmount':             0,
'EyeRightYSliderInc':                1,
'EyeRightYSliderInfoText':           'Eye Left Y-DIRECTION AMOUNT:\nShifts the eye right detection point up and down',
'EyeRightYSliderMax':                100,
'EyeRightYSliderMin':                -100,
'NoseXSliderAmount':                 0,
'NoseXSliderInc':                    1,
'NoseXSliderInfoText':               'Nose X-DIRECTION AMOUNT:\nShifts the nose detection point left and right',
'NoseXSliderMax':                    100,
'NoseXSliderMin':                    -100,
'NoseYSliderAmount':                 0,
'NoseYSliderInc':                    1,
'NoseYSliderInfoText':               'Nose Y-DIRECTION AMOUNT:\nShifts the nose detection point up and down',
'NoseYSliderMax':                    100,
'NoseYSliderMin':                    -100,
'MouthLeftXSliderAmount':            0,
'MouthLeftXSliderInc':               1,
'MouthLeftXSliderInfoText':          'Mouth Left X-DIRECTION AMOUNT:\nShifts the mouth left detection point left and right',
'MouthLeftXSliderMax':               100,
'MouthLeftXSliderMin':               -100,
'MouthLeftYSliderAmount':            0,
'MouthLeftYSliderInc':               1,
'MouthLeftYSliderInfoText':          'Mouth Left Y-DIRECTION AMOUNT:\nShifts the mouth left detection point up and down',
'MouthLeftYSliderMax':               100,
'MouthLeftYSliderMin':               -100,
'MouthRightXSliderAmount':           0,
'MouthRightXSliderInc':              1,
'MouthRightXSliderInfoText':         'Mouth Right X-DIRECTION AMOUNT:\nShifts the mouth Right detection point left and right',
'MouthRightXSliderMax':              100,
'MouthRightXSliderMin':              -100,
'MouthRightYSliderAmount':           0,
'MouthRightYSliderInc':              1,
'MouthRightYSliderInfoText':         'Mouth Right Y-DIRECTION AMOUNT:\nShifts the mouth Right detection point up and down',
'MouthRightYSliderMax':              100,
'MouthRightYSliderMin':              -100,
#
'DiffSliderAmount':                 4,
'DiffSliderInc':                    1,
'DiffSliderInfoText':               'DIFFERENCING AMOUNT:\nHigher values relaxes the similarity constraint.',
'DiffSliderMax':                    100,
'DiffSliderMin':                    0,
'FaceParserSliderAmount':           0,
'FaceParserSliderInc':              1,
'FaceParserSliderInfoText':         'BACKGROUND MASK AMOUNT:\nNegative/Positive values shrink and grow the mask.',
'FaceParserSliderMax':              50,
'FaceParserSliderMin':              -50,
'FaceScaleSliderAmount':            0,
'FaceScaleSliderInc':               1,
'FaceScaleSliderInfoText':          'FACE SCALE AMOUNT',
'FaceScaleSliderMax':               20,
'FaceScaleSliderMin':               -20,
'KPSScaleSliderAmount':             0,
'KPSScaleSliderInc':                1,
'KPSScaleSliderInfoText':           'KPS SCALE AMOUNT:\nGrows and shrinks the detection point distances.',
'KPSScaleSliderMax':                100,
'KPSScaleSliderMin':                -100,
'KPSXSliderAmount':                 0,
'KPSXSliderInc':                    1,
'KPSXSliderInfoText':               'KPS X-DIRECTION AMOUNT:\nShifts the detection points left and right',
'KPSXSliderMax':                    100,
'KPSXSliderMin':                    -100,
'KPSYSliderAmount':                 0,
'KPSYSliderInc':                    1,
'KPSYSliderInfoText':               'KPS Y-DIRECTION AMOUNT:\nShifts the detection points lup and down',
'KPSYSliderMax':                    100,
'KPSYSliderMin':                    -100,
'MouthParserSliderAmount':          0,
'MouthParserSliderInc':             1,
'MouthParserSliderInfoText':        'MOUTH MASK AMOUNT:\nAdjust the size of the mask. Mask the inside of the mouth, including the tongue',
'MouthParserSliderMax':             30,
'MouthParserSliderMin':             0,

'NeckParserSliderAmount':          0,
'NeckParserSliderInc':             1,
'NeckParserSliderInfoText':        'NECK MASK AMOUNT:\nAdjust the size of the mask.',
'NeckParserSliderMax':             30,
'NeckParserSliderMin':             0,

'LeftEyeBrowParserSliderAmount':          0,
'LeftEyeBrowParserSliderInc':             1,
'LeftEyeBrowParserSliderInfoText':        'LEFT EYEBROW MASK AMOUNT:\nAdjust the size of the mask.',
'LeftEyeBrowParserSliderMax':             30,
'LeftEyeBrowParserSliderMin':             0,

'RightEyeBrowParserSliderAmount':          0,
'RightEyeBrowParserSliderInc':             1,
'RightEyeBrowParserSliderInfoText':        'RIGHT EYEBROW MASK AMOUNT:\nAdjust the size of the mask.',
'RightEyeBrowParserSliderMax':             30,
'RightEyeBrowParserSliderMin':             0,

'LeftEyeParserSliderAmount':          0,
'LeftEyeParserSliderInc':             1,
'LeftEyeParserSliderInfoText':        'LEFT EYE MASK AMOUNT:\nAdjust the size of the mask.',
'LeftEyeParserSliderMax':             30,
'LeftEyeParserSliderMin':             0,

'RightEyeParserSliderAmount':          0,
'RightEyeParserSliderInc':             1,
'RightEyeParserSliderInfoText':        'RIGHT EYE MASK AMOUNT:\nAdjust the size of the mask.',
'RightEyeParserSliderMax':             30,
'RightEyeParserSliderMin':             0,

'NoseParserSliderAmount':          0,
'NoseParserSliderInc':             1,
'NoseParserSliderInfoText':        'NOSE MASK AMOUNT:\nAdjust the size of the mask.',
'NoseParserSliderMax':             30,
'NoseParserSliderMin':             0,

'UpperLipParserSliderAmount':          0,
'UpperLipParserSliderInc':             1,
'UpperLipParserSliderInfoText':        'UPPER LIP MASK AMOUNT:\nAdjust the size of the mask.',
'UpperLipParserSliderMax':             30,
'UpperLipParserSliderMin':             0,

'LowerLipParserSliderAmount':          0,
'LowerLipParserSliderInc':             1,
'LowerLipParserSliderInfoText':        'LOWER LIP MASK AMOUNT:\nAdjust the size of the mask.',
'LowerLipParserSliderMax':             30,
'LowerLipParserSliderMin':             0,

'RestoreEyesSliderAmount':               50,
'RestoreEyesSliderInc':                  1,
'RestoreEyesSliderInfoText':             'EYES BLEND SLIDER :\nIncrease this to show more of the swapped eyes. Decrease it to show more of the original eyes',
'RestoreEyesSliderMax':                  100,
'RestoreEyesSliderMin':                  1,

'RestoreEyesSizeSliderAmount':               3,
'RestoreEyesSizeSliderInc':                  0.5,
'RestoreEyesSizeSliderInfoText':             'EYES SIZE FACTOR :\nReduce this when swapping faces zoomed out of the frame.',
'RestoreEyesSizeSliderMax':                  4,
'RestoreEyesSizeSliderMin':                  2,

'Eyes_Mouth_BlurSliderAmount':               0,
'Eyes_Mouth_BlurSliderInc':                  1,
'Eyes_Mouth_BlurSliderInfoText':             'EYES AND MOUTH MASK BLUR:\nAdjust the blurring of the mask border.',
'Eyes_Mouth_BlurSliderMax':                  50,
'Eyes_Mouth_BlurSliderMin':                  0,
'RestoreEyesFeatherSliderAmount':               10,
'RestoreEyesFeatherSliderInc':                  1,
'RestoreEyesFeatherSliderInfoText':             'EYES FEATHER BLEND :\nAdjust the blending of eyes border. Increase this to show more of the original eyes. Decrease this to show more of the swapped eyes',
'RestoreEyesFeatherSliderMax':                  100,
'RestoreEyesFeatherSliderMin':                  1,

'RestoreEyesRadiusFactorXSliderAmount':      1.0,
'RestoreEyesRadiusFactorXSliderInc':         0.1,
'RestoreEyesRadiusFactorXSliderInfoText':    'X EYES RADIUS FACTOR :\nThese parameters determine the shape of the mask. If both are equal to 1.0, the mask will be circular. If either one is greater or less than 1.0, the mask will become oval, stretching or shrinking along the corresponding direction.',
'RestoreEyesRadiusFactorXSliderMax':         3.0,
'RestoreEyesRadiusFactorXSliderMin':         0.3,

'RestoreEyesRadiusFactorYSliderAmount':      1.0,
'RestoreEyesRadiusFactorYSliderInc':         0.1,
'RestoreEyesRadiusFactorYSliderInfoText':    'Y EYES RADIUS FACTOR :\nThese parameters determine the shape of the mask. If both are equal to 1.0, the mask will be circular. If either one is greater or less than 1.0, the mask will become oval, stretching or shrinking along the corresponding direction.',
'RestoreEyesRadiusFactorYSliderMax':         3.0,
'RestoreEyesRadiusFactorYSliderMin':         0.3,

'RestoreEyesXoffsetSliderAmount':      0,
'RestoreEyesXoffsetSliderInc':         1,
'RestoreEyesXoffsetSliderInfoText':    'X REPAIR EYES OFFSET SLIDER :\nMove the mask along the X-axis.',
'RestoreEyesXoffsetSliderMax':         300,
'RestoreEyesXoffsetSliderMin':         -300,
'RestoreEyesYoffsetSliderAmount':      0,
'RestoreEyesYoffsetSliderInc':         1,
'RestoreEyesYoffsetSliderInfoText':    'Y REPAIR EYES OFFSET SLIDER :\nMove the mask along the Y-axis.',
'RestoreEyesYoffsetSliderMax':         300,
'RestoreEyesYoffsetSliderMin':         -300,
'RestoreEyesSpacingOffsetSliderAmount':      0,
'RestoreEyesSpacingOffsetSliderInc':         1,
'RestoreEyesSpacingOffsetSliderInfoText':    'Y REPAIR EYES SPACING OFFSET SLIDER :\nChange the distance between the eyes.',
'RestoreEyesSpacingOffsetSliderMax':         200,
'RestoreEyesSpacingOffsetSliderMin':         -200,
'RestoreMouthSliderAmount':               50,
'RestoreMouthSliderInc':                  1,
'RestoreMouthSliderInfoText':             'MOUTH BLEND :\nIncrease this to show more of the swapped Mouth. Decrease it to show more of the original Mouth',
'RestoreMouthSliderMax':                  100,
'RestoreMouthSliderMin':                  1,

'RestoreMouthSizeSliderAmount':               25,
'RestoreMouthSizeSliderInc':                  5,
'RestoreMouthSizeSliderInfoText':             'MOUTH SIZE :\nIncrease this when swapping faces zoomed out of the frame.',
'RestoreMouthSizeSliderMax':                  60,
'RestoreMouthSizeSliderMin':                  5,

'RestoreMouthFeatherSliderAmount':               10,
'RestoreMouthFeatherSliderInc':                  1,
'RestoreMouthFeatherSliderInfoText':             'MOUTH FEATHER BLEND :\nAdjust the border of Mouth blending. Increase this to show more of the original Mouth. Decrease this to show more of the swapped Mouth',
'RestoreMouthFeatherSliderMax':                  100,
'RestoreMouthFeatherSliderMin':                  1,

'RestoreMouthRadiusFactorXSliderAmount':      1.0,
'RestoreMouthRadiusFactorXSliderInc':         0.1,
'RestoreMouthRadiusFactorXSliderInfoText':    'X MOUTH RADIUS FACTOR :\nThese parameters determine the shape of the mask. If both are equal to 1.0, the mask will be circular. If either one is greater or less than 1.0, the mask will become oval, stretching or shrinking along the corresponding direction.',
'RestoreMouthRadiusFactorXSliderMax':         3.0,
'RestoreMouthRadiusFactorXSliderMin':         0.3,

'RestoreMouthRadiusFactorYSliderAmount':      1.0,
'RestoreMouthRadiusFactorYSliderInc':         0.1,
'RestoreMouthRadiusFactorYSliderInfoText':    'Y MOUTH RADIUS FACTOR :\nThese parameters determine the shape of the mask. If both are equal to 1.0, the mask will be circular. If either one is greater or less than 1.0, the mask will become oval, stretching or shrinking along the corresponding direction.',
'RestoreMouthRadiusFactorYSliderMax':         3.0,
'RestoreMouthRadiusFactorYSliderMin':         0.3,

'RestoreMouthXoffsetSliderAmount':      0,
'RestoreMouthXoffsetSliderInc':         1,
'RestoreMouthXoffsetSliderInfoText':    'X REPAIR MOUTH OFFSET SLIDER :\nMove the mouth mask along the X-axis.',
'RestoreMouthXoffsetSliderMax':         300,
'RestoreMouthXoffsetSliderMin':         -300,
'RestoreMouthYoffsetSliderAmount':      0,
'RestoreMouthYoffsetSliderInc':         1,
'RestoreMouthYoffsetSliderInfoText': 'Y REPAIR MOUTH OFFSET SLIDER :\nMove the mouth mask along the Y-axis.',
'RestoreMouthYoffsetSliderMax':         300,
'RestoreMouthYoffsetSliderMin':         -300,
'OccluderSliderAmount':             0,
'OccluderSliderInc':                1,
'OccluderSliderInfoText':           'OCCLUDER AMOUNT:\nGrows or shrinks the occluded region',
'OccluderSliderMax':                100,
'OccluderSliderMin':                -100,
'DFLXSegSliderAmount':             0,
'DFLXSegSliderInc':                1,
'DFLXSegSliderInfoText':           'DFL XSeg AMOUNT:\nGrows or shrinks the occluded region',
'DFLXSegSliderMax':                100,
'DFLXSegSliderMin':                -100,
'OccluderBlurSliderAmount':             5,
'OccluderBlurSliderInc':                1,
'OccluderBlurSliderInfoText':           'OCCLUDER BLUR:\nThe blending value between the occluder and XSeg.',
'OccluderBlurSliderMax':                100,
'OccluderBlurSliderMin':                0,
'ParserBlurSliderAmount':             5,
'ParserBlurSliderInc':                1,
'ParserBlurSliderInfoText':           'FACE PARSER BLUR:\nThe blending value of the FaceParser.',
'ParserBlurSliderMax':                100,
'ParserBlurSliderMin':                0,
'BGParserBlurSliderAmount':             5,
'BGParserBlurSliderInc':                1,
'BGParserBlurSliderInfoText':           'BG FACE PARSER BLUR:\nThe blending value of the BG FaceParser.',
'BGParserBlurSliderMax':                100,
'BGParserBlurSliderMin':                0,
'DiffingBlurSliderAmount':                5,
'DiffingBlurSliderInc':                   1,
'DiffingBlurSliderInfoText':              'COMPARISON BLEND:\nThe blending value of the comparison.',
'DiffingBlurSliderMax':                   100,
'DiffingBlurSliderMin':                   0,
'OrientSliderAmount':               0,
'OrientSliderInc':                  90,
'OrientSliderInfoText':             'ORIENTATION ANGLE:\nSet this to the angle of the input face angle to help with laying down/upside down/etc. Angles are read clockwise.',
'OrientSliderMax':                  270,
'OrientSliderMin':                  0,
'RestorerSliderAmount':             100,
'RestorerSliderInc':                5,
'RestorerSliderInfoText':           'RESTORER AMOUNT:\nBlends the Restored results back into the original swap.',
'RestorerSliderMax':                100,
'RestorerSliderMin':                0,
'Restorer2SliderAmount':             100,
'Restorer2SliderInc':                5,
'Restorer2SliderInfoText':           '2. REPAIR AMOUNT:\nBlends the repaired result back into the original swap.',
'Restorer2SliderMax':                100,
'Restorer2SliderMin':                0,
'EnhancerSliderAmount':             100,
'EnhancerSliderInc':                5,
'EnhancerSliderInfoText':           'ENHANCER AMOUNT:\nBlends the enhanced results back into the original frame.',
'EnhancerSliderMax':                100,
'EnhancerSliderMin':                0,
'StrengthSliderAmount':             100,
'StrengthSliderInc':                25,
'StrengthSliderInfoText':           'STRENGTH AMOUNT:\nIncrease up to 5x additional swaps (500%). 200% is generally a good result. Set to 0 to turn off swapping but allow the rest of the pipeline to apply to the original image.',
'StrengthSliderMax':                500,
'StrengthSliderMin':                0,
'ThreadsSliderAmount':              5,
'ThreadsSliderInc':                 1,
'ThreadsSliderInfoText':            'EXECUTION THREADS:\nSet number of execution threads while playing and recording. Depends strongly on GPU VRAM. 5 threads for 24GB.',
'ThreadsSliderMax':                 50,
'ThreadsSliderMin':                 1,
'ThresholdSliderAmount':            55,
'ThresholdSliderInc':               1,
'ThresholdSliderInfoText':          'THRESHHOLD AMOUNT:\nRaise to reduce faces hopping around when swapping multiple people. A higher value is stricter.',
'ThresholdSliderMax':               100,
'ThresholdSliderMin':               0,
'VideoQualSliderAmount':            18,
'VideoQualSliderInc':               1,
'VideoQualSliderInfoText':          'VIDEO QUALITY:\nThe encoding quality of the recorded video. 0 is best, 50 is worst, 18 is mostly lossless. File size increases with a lower quality number.',
'VideoQualSliderMax':               50,
'VideoQualSliderMin':               0,

'DFLAmpMorphSliderAmount':          50,
'DFLAmpMorphSliderInc':             1,
'DFLAmpMorphSliderInfoText':        'DFL AMP MORPH FACTOR\n:Set the morph factor when using DFL AMP Models',
'DFLAmpMorphSliderMax':             100,
'DFLAmpMorphSliderMin':             1,

'AudioSpeedSliderAmount':           1.00,
'AudioSpeedSliderInc':              0.01,
'AudioSpeedSliderInfoText':         'AUDIO PLAYBACK SPEED:\nAudo playback when "Enable Audio" is on',
'AudioSpeedSliderMax':              2.00,
'AudioSpeedSliderMin':              0.50,

'VQFRFidelitySliderAmount':         0.0,
'VQFRFidelitySliderInc':            0.1,
'VQFRFidelitySliderInfoText':       'VQFR Fidelity Ratio:\nFidelity ratio value for VQFR v2 restorer.',
'VQFRFidelitySliderMax':            1.0,
'VQFRFidelitySliderMin':            0.0,

'CLIPSliderAmount':                 50,
'CLIPSliderInc':                    1,
'CLIPSliderInfoText':               'TEXT MASKING STENGTH:\nIncrease to strengthen the effect.',
'CLIPSliderMax':                    100,
'CLIPSliderMin':                    0,

'ColorGammaSliderAmount':                 1,
'ColorGammaSliderInc':                    0.02,
'ColorGammaSliderInfoText':               'GAMMA VALUE:\nChanges Gamma.',
'ColorGammaSliderMax':                    2,
'ColorGammaSliderMin':                    0,

'NoiseSliderAmount':                 0,
'NoiseSliderInc':                    0.5,
'NoiseSliderInfoText':               'GAMMA VALUE:\nAdds noise to the swapped face.',
'NoiseSliderMax':                    20,
'NoiseSliderMin':                    0,
'ColorBrightSliderAmount':                 1,
'ColorBrightSliderInc':                    0.01,
'ColorBrightSliderInfoText':               'Bright VALUE:\nChanges Bright.',
'ColorBrightSliderMax':                    2,
'ColorBrightSliderMin':                    0,

'ColorContrastSliderAmount':                 1,
'ColorContrastSliderInc':                    0.01,
'ColorContrastSliderInfoText':               'Contrast VALUE:\nChanges Contrast.',
'ColorContrastSliderMax':                    2,
'ColorContrastSliderMin':                    0,

'ColorSaturationSliderAmount':                 1,
'ColorSaturationSliderInc':                    0.01,
'ColorSaturationSliderInfoText':               'Saturation VALUE:\nChanges Saturation.',
'ColorSaturationSliderMax':                    2,
'ColorSaturationSliderMin':                    0,

'ColorSharpnessSliderAmount':                 1,
'ColorSharpnessSliderInc':                    0.1,
'ColorSharpnessSliderInfoText':               'Sharpness VALUE:\nChanges Sharpness.',
'ColorSharpnessSliderMax':                    2,
'ColorSharpnessSliderMin':                    0,

'ColorHueSliderAmount':                 0,
'ColorHueSliderInc':                    0.01,
'ColorHueSliderInfoText':               'Hue VALUE:\nChanges Hue.',
'ColorHueSliderMax':                    0.5,
'ColorHueSliderMin':                    -0.5,

# Text Selection
'DetectTypeTextSelInfoText':        'FACE DETECTION MODEL:\nSelect the face detection model. Mostly only subtle differences, but can significant differences when the face is at extreme angles or covered.',
'DetectTypeTextSelMode':            'Retinaface',
'DetectTypeTextSelModes':           ['Retinaface', 'Yolov8', 'SCRDF', 'Yunet'],
# Face Landmarks Detection
'LandmarksDetectTypeTextSelInfoText': 'LANDMARKS FACE DETECTION MODEL:\nSelect the landmarks face detection model. Mostly only subtle differences, but can significant differences when the face is at extreme angles or covered.',
'LandmarksDetectTypeTextSelMode':     '203',
'LandmarksDetectTypeTextSelModes':    ['5', '68', '3d68', '98', '106', '203', '478'],
#
# Similarity Type
'SimilarityTypeTextSelInfoText':    'Similarity version:\nSelect the similarity to be used with arc face recognizer model.',
'SimilarityTypeTextSelMode':        'Opal',
'SimilarityTypeTextSelModes':       ['Opal', 'Pearl', 'Optimal'],
#
# ProvidersPriority
'ProvidersPriorityTextSelInfoText':    'Providers Priority:\nSelect the providers priority to be used with the system.',
'ProvidersPriorityTextSelMode':        'CUDA',
'ProvidersPriorityTextSelModes':       ['CUDA', 'TensorRT', 'TensorRT-Engine', 'CPU'],
#
# Face Swapper Model
'FaceSwapperModelTextSelInfoText':  'Face Swapper Model:\nSelect the Face Swapper model.',
'FaceSwapperModelTextSelMode':      'Inswapper128',
'FaceSwapperModelTextSelModes':     ['Inswapper128', 'SimSwap512', 'GhostFace-v1', 'GhostFace-v2', 'GhostFace-v3'],
#
'PreviewModeTextSelInfoText':       '',
'PreviewModeTextSelMode':           'Video',
'PreviewModeTextSelModes':          ['Video', 'Image','Theater'],
'RecordTypeTextSelInfoText':        'VIDEO RECORDING LIBRARY:\nSelect the recording library used for video recording. FFMPEG uses the Video Quality slider to adjust the size and quality of the final video. OPENCV has no options but is faster and produces good results.',
'RecordTypeTextSelMode':            'FFMPEG',
'RecordTypeTextSelModes':           ['FFMPEG', 'OPENCV'],
'RestorerDetTypeTextSelInfoText':   'Choose face alignment: \nOriginal preserves facial features and expressions, but may show some artifacts. Reference softens features. Blend is closer to Reference but much faster.',
'RestorerDetTypeTextSelMode':       'Blend',
'RestorerDetTypeTextSelModes':      ['Original', 'Blend', 'Reference'],
'RestorerTypeTextSelInfoText':      'RESTORER TYPE:\nSelect the Restorer type.\nSpeed: GPEN 256>GFPGAN v1.4>CodeFormer>GPEN 512>GPEN 1024>GPEN 2048>VQFR v2',
'RestorerTypeTextSelMode':          'GFPGAN-v1.4',
'RestorerTypeTextSelModes':         ['GFPGAN-v1.4', 'CodeFormer', 'GPEN-256', 'GPEN-512', 'GPEN-1024', 'GPEN-2048', 'RestoreFormer++', 'VQFR-v2'],
'Restorer2DetTypeTextSelInfoText':   'ALIGNMENT:\nSelect how the face is aligned for the Restorer. Original preserves facial features and expressions, but can show some artifacts. Reference softens features. Blend is closer to Reference but is much faster.',
'Restorer2DetTypeTextSelMode':       'Blend',
'Restorer2DetTypeTextSelModes':      ['Original', 'Blend', 'Reference'],
'Restorer2TypeTextSelInfoText':      'RESTORER TYPE:\nSelect the Restorer type.\nSpeed: GPEN 256>GFPGAN v1.4>CodeFormer>GPEN 512>GPEN 1024>GPEN 2048>VQFR v2',
'Restorer2TypeTextSelMode':          'GFPGAN-v1.4',
'Restorer2TypeTextSelModes':         ['GFPGAN-v1.4', 'CodeFormer', 'GPEN-256', 'GPEN-512', 'GPEN-1024', 'GPEN-2048', 'RestoreFormer++', 'VQFR-v2'],

# Frame Enhancer
'FrameEnhancerTypeTextSelInfoText': 'FRAME ENHANCER TYPE:\nSelect the Restorer type.\nSpeed: DeOldify Artistic>DeOldify Stable>DeOldify Video>DDColor Artistic>DDColor>RealEsrgan x2 plus>BSRGan x2>UltraMix x4>UltraSharp x4>RealEsrgan x4 plus>BSRGan x4',
'FrameEnhancerTypeTextSelMode':     'RealEsrgan-x2-Plus',
'FrameEnhancerTypeTextSelModes':    ['RealEsrgan-x2-Plus', 'RealEsrgan-x4-Plus', 'RealEsr-General-x4v3', 'BSRGan-x2', 'BSRGan-x4', 'UltraSharp-x4', 'UltraMix-x4', 'DDColor-Artistic', 'DDColor', 'DeOldify-Artistic', 'DeOldify-Stable', 'DeOldify-Video'],
#

# AutoColor
'AutoColorTypeTextSelInfoText':      'AUTO COLOR TYPE:\nSelect the method.\nFor testing, the Hans method sometimes shows artifacts.',
'AutoColorTypeTextSelMode':          'Test',
'AutoColorTypeTextSelModes':         ['Test', 'Test_Mask', 'DFL_Test', 'DFL_Orig'],

# WebCam
'WebCamMaxResolSelInfoText':        "WEBCAM MAX RESOLUTION:\nSelect the maximum resolution to be used by the webcam",
'WebCamMaxResolSelMode':            '1920x1080',
'WebCamMaxResolSelModes':           ['480x360', '640x480', '1280x720', '1920x1080','2560x1440','3840x2160'],

'WebCamBackendSelInfoText':        "WEBCAM BACKEND:\nSelect the backend for selecting camera (Choose based on your OS)",
'WebCamBackendSelMode':            'Default',
'WebCamBackendSelModes':           ['Default', 'DirectShow','MSMF', 'V4L', 'V4L2','GSTREAMER'],

'WebCamMaxFPSSelInfoText':        "WEBCAM MAX FPS:\nSelect the maximum FPS of the Webcam",
'WebCamMaxFPSSelMode':            30,
'WebCamMaxFPSSelModes':           [23,30,60],

'WebCamMaxNoSliderAmount':                 1,
'WebCamMaxNoSliderInc':                    1,
'WebCamMaxNoSliderInfoText':               'WEBCAM MAX COUNT:\nMaximum No of Webcams to be detected.',
'WebCamMaxNoSliderMax':                    10,
'WebCamMaxNoSliderMin':                    0,

'MergeTextSelInfoText':      'INPUT FACES MERGE MATH:\nWhen shift-clicking face for merging, determines how the embedding vectors are combined.',
'MergeTextSelMode':          'Mean',
'MergeTextSelModes':         ['Mean', 'Median'],
'SwapperTypeTextSelInfoText':      'SWAPPER OUTPUT RESOLUTION:\nDetermines the resolution of the swapper output.',
'SwapperTypeTextSelMode':          '128',
'SwapperTypeTextSelModes':         ['128', '256', '512'],
'QualityModeTextSelInfoText':      'Quality Mode:\nFast: Fastest speed, basic quality\nNormal: Balanced speed and quality\nHigh: Better quality, slower\nUltra: Best quality, slowest',
'QualityModeTextSelMode':          'Normal',
'QualityModeTextSelModes':         ['Fast', 'Normal', 'High', 'Ultra'],
'UseEnhancedSwapSwitchInfoText':   'Enhanced Face Swap:\nEnable enhanced algorithms\nfor better swap quality',
'UseEnhancedSwapSwitchState':      True,

# Text Entry
'CLIPTextEntry':    '',
'CLIPTextEntryInfoText':            'TEXT MASKING ENTRY:\nTo use, type a word(s) in the box separated by commas and press <enter>.',

# Face Editor
'FaceEditorTypeTextSelInfoText':   'Face Editor Type:\nSelect the target type to be edited in Face Editor.',
'FaceEditorTypeTextSelMode':       'Human-Face',
#'FaceEditorTypeTextSelModes':      ['Human-Face', 'Animal-Face'],
'FaceEditorTypeTextSelModes':      ['Human-Face'],
'FaceEditorType': 'Face Editor Type',

'FaceEditorIDSliderAmount':        1,
'FaceEditorIDSliderInc':           1,
'FaceEditorIDSliderInfoText':      'FACE EDITOR POSITION FACE ID:\nDetermines the target face in the frame that can be modified.',
'FaceEditorIDSliderMax':           20,
'FaceEditorIDSliderMin':           1,
'FaceEditorID': 'Face Editor ID: ',
'CropScaleSliderAmount':           2.50,
'CropScaleSliderInc':              0.05,
'CropScaleSliderInfoText':         'Crop Scale:\nChanges source crop scale.',
'CropScaleSliderMax':              3.20,
'CropScaleSliderMin':              1.80,
'CropScale': 'Crop Scale',

'EyesOpenRatioSliderAmount':       0.00,
'EyesOpenRatioSliderInc':          0.01,
'EyesOpenRatioSliderInfoText':     'Eyes Open Ratio:\nChanges the opening of the eyes.',
'EyesOpenRatioSliderMax':          0.80,
'EyesOpenRatioSliderMin':          -0.80,
'EyesOpenRatio': 'Eyes Close <--> Open Ratio: ',


'LipsOpenRatioSliderAmount':       0.00,
'LipsOpenRatioSliderInc':          0.01,
'LipsOpenRatioSliderInfoText':     'Lips Open Ratio:\nChanges the opening of the lips.',
'LipsOpenRatioSliderMax':          0.80,
'LipsOpenRatioSliderMin':          -0.80,
'LipsOpenRatio': 'Lips Close <--> Open Ratio: ',
'HeadPitchSliderAmount':           0,
'HeadPitchSliderInc':              1,
'HeadPitchSliderInfoText':         'Head Pitch:\nChanges the head pitch.',
'HeadPitchSliderMax':              15,
'HeadPitchSliderMin':              -15,
'HeadPitch': 'Head Pitch: ',

'HeadYawSliderAmount':             0,
'HeadYawSliderInc':                1,
'HeadYawSliderInfoText':           'Head Yaw:\nChanges the head yaw.',
'HeadYawSliderMax':                15,
'HeadYawSliderMin':                -15,
'HeadYaw': 'Head Yaw: ',

'HeadRollSliderAmount':            0,
'HeadRollSliderInc':               1,
'HeadRollSliderInfoText':          'Head Roll:\nChanges the head roll.',
'HeadRollSliderMax':               15,
'HeadRollSliderMin':               -15,
'HeadRoll': 'Head Roll: ',

'XAxisMovementSliderAmount':       0.00,
'XAxisMovementSliderInc':          0.01,
'XAxisMovementSliderInfoText':     'X-Axis Movement:\nChanges the head direction x-axis.',
'XAxisMovementSliderMax':          0.19,
'XAxisMovementSliderMin':          -0.19,
'XAxisMovement': 'X-Axis Movement: ',
'YAxisMovementSliderAmount':       0.00,
'YAxisMovementSliderInc':          0.01,
'YAxisMovementSliderInfoText':     'Y-Axis Movement:\nChanges the head direction y-axis.',
'YAxisMovementSliderMax':          0.19,
'YAxisMovementSliderMin':          -0.19,
'YAxisMovement': 'Y-Axis Movement: ',
'ZAxisMovementSliderAmount':       1.00,
'ZAxisMovementSliderInc':          0.01,
'ZAxisMovementSliderInfoText':     'Z-Axis Movement:\nChanges the head direction z-axis.',
'ZAxisMovementSliderMax':          1.20,
'ZAxisMovementSliderMin':          -0.90,
'ZAxisMovement': 'Z-Axis Movement: ',

'MouthPoutingSliderAmount':        0.00,
'MouthPoutingSliderInc':           0.01,
'MouthPoutingSliderInfoText':      'Mouth Pouting:\nPouting the mouth.',
'MouthPoutingSliderMax':           0.09,
'MouthPoutingSliderMin':           -0.09,
'MouthPouting': 'Mouth Pouting: ',

'MouthPursingSliderAmount':        0.00,
'MouthPursingSliderInc':           0.01,
'MouthPursingSliderInfoText':      'Mouth Pursing:\nPursing the mouth.',
'MouthPursingSliderMax':           15.00,
'MouthPursingSliderMin':           -20.00,
'MouthPursing': 'Mouth Pursing: ',

'MouthGrinSliderAmount':           0.00,
'MouthGrinSliderInc':              0.01,
'MouthGrinSliderInfoText':         'Mouth Grin:\nChanges the mouth grin.',
'MouthGrinSliderMax':              15.00,
'MouthGrinSliderMin':              0.00,
'MouthGrin': 'Mouth Grin: ',

'LipsCloseOpenSliderAmount':       0,
'LipsCloseOpenSliderInc':          1,
'LipsCloseOpenSliderInfoText':     'Lips Close <--> Open :\nChanges the closing or opening of the lips.',
'LipsCloseOpenSliderMax':          120,
'LipsCloseOpenSliderMin':          -90,
'LipsCloseOpen': 'Lips Close <--> Open: ',
'MouthSmileSliderAmount':          0.00,
'MouthSmileSliderInc':             0.01,
'MouthSmileSliderInfoText':        'Mouth Smile:\nChanges the mouth smile.',
'MouthSmileSliderMax':             1.30,
'MouthSmileSliderMin':             -0.30,
'MouthSmile': 'Mouth Smile: ',

'EyeWinkSliderAmount':             0.00,
'EyeWinkSliderInc':                0.01,
'EyeWinkSliderInfoText':           'Eye Wink:\nWinking eye.',
'EyeWinkSliderMax':                39.0,
'EyeWinkSliderMin':                0.0,
'EyeWink': 'Eye Wink: ',
'EyeBrowsDirectionSliderAmount':   0.00,
'EyeBrowsDirectionSliderInc':      0.01,
'EyeBrowsDirectionSliderInfoText': 'EyeBrows Direction:\nChanges the eyebrows direction.',
'EyeBrowsDirectionSliderMax':      30.00,
'EyeBrowsDirectionSliderMin':      -30.00,
'EyeBrowsDirection': 'EyeBrows Direction: ',
'EyeGazeHorizontalSliderAmount':   0.00,
'EyeGazeHorizontalSliderInc':      0.01,
'EyeGazeHorizontalSliderInfoText': 'EyeGaze Horizontal:\nChanges the horizontal eyegaze direction.',
'EyeGazeHorizontalSliderMax':      30.00,
'EyeGazeHorizontalSliderMin':      -30.00,
'EyeGazeHorizontal': 'EyeGaze Horizontal: ',


'EyeGazeVerticalSliderAmount':     0.00,
'EyeGazeVerticalSliderInc':        0.01,
'EyeGazeVerticalSliderInfoText':   'EyeGaze Vertical:\nChanges the vertical eyegaze direction.',
'EyeGazeVerticalSliderMax':        63.00,
'EyeGazeVerticalSliderMin':        -63.00,
'EyeGazeVertical': 'EyeGaze Vertical: ',

# GUI Text
'WindowTitle': 'Rope-Live Stellar',
'StarPluckWindowTitle': 'StarPluck',
'TargetVideoTitle': 'TARGET VIDEO',
'DesiredAvatarTitle': 'DESIRED AVATAR',
'FoundFacesTitle': 'FOUND FACES',
'MergedFacesTitle': 'MERGED FACES',
'DFMModelTitle': 'DFM MODEL FILE',
'SettingsTitle': 'Settings',
'GetStreamKeyTitle': 'Get Key',
'OBSStreamTitle': 'OBS Setup',
'CameraExtensionTitle': 'Camera Extension',
'LanguageSettingsTitle': 'Language',
'NpcapErrorTitle': 'ERROR',
'NpcapErrorMessage': 'Npcap is not installed. Please visit https://npcap.com to download and install Npcap.',
'StreamSettingsSuccess': 'OBS stream settings successful. Start streaming now.',
'StreamSettingsWarning': 'OBS stream settings may not have been updated successfully. Please manually check your OBS settings.',
'StreamSettingsError': 'Error setting OBS:',
'NoStreamInfoError': 'Failed to retrieve stream information. Please ensure that the live program is open.',
'LanguageChangeSuccess': 'Language switch successful. The program will restart automatically.',
'LanguageChangeError': 'Language switch failed:',
'InputPanelTitle': 'Input Panel',
'FacesPanelTitle': 'Faces Panel',
'ParamsPanelTitle': 'Parameters Panel',
'KeyboardShortcutsTitle': 'Keyboard Shortcuts',
'ParametersVisibilityTitle': 'Parameters Visibility',
'LivePortraitTitle': 'Face Editor  ',
'FaceSwapperTitle': 'Face Swapper  ',
'FaceEditorTitle': 'Face Editor',
'FaceSwapperParametersTitle': 'Face Swap Params',
'ThreadsTitle': 'Threads',
'FaceSwapperModelTitle': 'FSM',
'SwapperResolution': 'SwapRes',
'QualityModeTitle': 'Quality Mode',
'UseEnhancedSwapTitle': 'Enhanced Swap',
'WebcamBackendTitle': 'CamBack',
'WebcamResolution': 'CamRes',
'WebcamFPS': 'Webcam FPS',
'WebcamCount': 'MaxWebCam',
'SendFramesToVirtualCamera': 'Send Frames to Virtual Camera',
'RestorerTitle': 'Restorer',
'RestorerModelTitle': 'Restorer Model',
'RestorerFidelity': 'Fidelity Ratio',
'Restorer2Title': 'Restorer2',
'Restorer2ModelTitle': 'Restorer Model',
'Restorer2DetectionAlignment': 'DetAlign',
'DetectionAlignment': 'DetAlign',
'Restorer2Blend': 'Restorer Blend',
'Restorer2Fidelity': 'Restorer Fidelity',
'RestorerBlend': 'Restorer Blend',
'EnhancerType': 'EnhType',
'FrameEnhancerBlend': 'Enhancer Blend',
'Orientation': 'Orientation',
'Strength': 'Strength',
'StrengthValue': 'Strength Value',
'BorderTop': 'Top Border Distance',
'BorderLeft': 'Left Border Distance',
'BorderRight': 'Right Border Distance',
'BorderBottom': 'Bottom Border Distance',
'BorderBlur': 'Border Blend',
'Diff': 'Differencing',
'DiffValue': 'Amount',
'DiffingBlur': 'Diffing Blur',
'Occluder': 'Occluder',
'OccluderSize': 'Size',
'DFLXSegSliderSize': 'Size',
'DFLXSeg': 'DFL XSeg',
'OccluderBlur': 'Occlusion Blur',
'FinalBlurSwitch': 'Final Blur',
'FinalBlurValue': 'Final Blur Value',
'OverallMaskBlend': 'Overall Mask Blend',
'DFLRCTColorTransfer': 'DFL RCT Color Transfer',
'DFLLoadOnlyOne': 'DFL Load Only One',
'DFLAmpMorphFactor': 'DFL AMP Morph Factor',
'OrientationAngle': 'Angle',
'CLIPValue': 'Amount',
'CLIP': 'CLIP',
'CLIPMask': 'CLIP Mask',
'CLIPText': 'CLIP Text',
'RestoreEyes': 'Restore Eyes',
'RestoreEyesBlend': 'Restore Eyes Blend',
'EyesMouthBlur': 'Eyes Mouth Blur',
'RestoreEyesFeather': 'Restore Eyes Feather',
'RestoreEyesSize': 'Restore Eyes Size',
'RestoreEyesRadiusFactorX': 'Restore Eyes Radius Factor X',
'RestoreEyesRadiusFactorY': 'Restore Eyes Radius Factor Y',
'RestoreEyesSpacingOffset': 'Restore Eyes Spacing Offset',
'RestoreEyesXoffset': 'Restore Eyes Xoffset',
'RestoreEyesYoffset': 'Restore Eyes Yoffset',
'RestoreMouth': 'Restore Mouth',
'RestoreMouthBlend': 'Restore Mouth Blend',
'RestoreMouthFeather': 'Restore Mouth Feather',
'RestoreMouthSize': 'Restore Mouth Size',
'RestoreMouthRadiusFactorX': 'Restore Mouth Radius Factor X',
'RestoreMouthRadiusFactorY': 'Restore Mouth Radius Factor Y',
'RestoreMouthXoffset': 'Restore Mouth Xoffset',
'RestoreMouthYoffset': 'Restore Mouth Yoffset',
'BGParserBlur': 'Background Parser Blur',
'ParserBlur': 'Parser Blur',
'FaceParserGB': 'Background',
'NeckParser': 'Neck Parser',
'LeftEyeBrowParser': 'Left Eye Brow Parser',
'RightEyeBrowParser': 'Right Eye Brow Parser',
'LeftEyeParser': 'Left Eye Parser',
'RightEyeParser': 'Right Eye Parser',
'NoseParser': 'Nose Parser',
'MouthParser': 'Mouth Parser',
'LipsParser': 'Lips Parser',
'UpperLipParser': 'Upper Lip Parser',
'LowerLipParser': 'Lower Lip Parser',
'FaceParser': 'Face Parser',
'AutoColor': 'Auto Color',
'AutoColorBlend': 'Auto Color Blend',
'SimilarityType': 'Similarity Type',
'JpegCompression': 'JPEG Compression',
'JpegCompressionValue': 'JPEG Compression Value',
'ColorAdjustment': 'Color Adjustment',
'ColorRed': 'Red',
'ColorGreen': 'Green',
'ColorBlue': 'Blue',
'ColorBright': 'Brightness',
'ColorContrast': 'Contrast',
'ColorSaturation': 'Saturation',
'ColorSharpness': 'Sharpness',
'ColorHue': 'Hue',
'ColorGamma': 'Gamma',
'Noise': 'Noise',
'FaceAdj': 'Input Face Adjustments',
'KPSX': 'KPS X',
'KPSY': 'KPS Y',
'KPSScale': 'KPS Scale',
'FaceScale': 'Face Scale',
'FaceLikeness': 'Face Likeness',
'FaceLikenessFactor': 'Face Likeness Factor',
'Threshold': 'Threshold',
'DetectType': 'Detect Type',
'DetectScore': 'Detect Score',
'AutoRotation': 'Auto Rotation',
'LandmarksDetectionAdj': 'Landmarks Detection Adjustments',
'LandmarksAlignModeFromPoints': 'From Points',
'LandmarksDetectType': 'Landmarks Detection Type',
'LandmarksDetectScore': 'Landmarks Detect Score',
'ShowLandmarks': 'Show Landmarks',
'LandmarksPositionAdj': 'Landmarks Position Adjustments',
'FaceID': 'Face ID',
'EyeLeftX': 'Eye Left X',
'EyeLeftY': 'Eye Left Y',
'EyeRightX': 'Eye Right X',
'EyeRightY': 'Eye Right Y',
'NoseX': 'Nose X',
'NoseY': 'Nose Y',
'MouthLeftX': 'Mouth Left X',
'MouthLeftY': 'Mouth Left Y',
'MouthRightX': 'Mouth Right X',
'MouthRightY': 'Mouth Right Y',
'RecordType': 'Record Type',
'VideoQual': 'FFMPEG Quality',
'AudioSpeed': 'Audio Playback Speed',
'Merge': 'Merge',
'HelpTextSel': 'Help Document',
'LicenseStatusText': 'Status: {0}',
'TrialText': 'Trial',
'TrialDays': 'Trial: {0} days left.',
'Remaining': '{0} days left.',
'Warning': 'Warning',
'LanguageSwitched': 'Language switched successfully. Click OK to restart for the changes to take effect.',
'OK': 'OK',
'Error': 'Error',
'LanguageChangeError': 'Language switch failed:',
'GetStreamKeyError': 'Error getting stream key:',
'OBSStreamSuccess': 'OBS stream settings successful, start streaming now.',
'OBSStreamWarning': 'OBS stream settings may not have been updated successfully. Please manually check your OBS settings.',
'CurrentStreamSettings': 'Current stream service settings information:',
'ActivationTitle': 'Activation Required',
'ActivationSuccess': 'Activation Success',
'ActivationSuccessMsg': 'Validity period: {0}',
'ActivationError': 'Error',
'InvalidActivationCode': 'Invalid or expired activation code',
'ActivationCodeLabel': 'Enter Code:',
'ActivateButton': 'Activate',
'BuyActivationCodeLabel': 'Trial ended. Buy activation code.\nStay online to activate.',
'BuyActivationCode': 'Purchase',
'BuyActivationCodeLink': 'https://stellar.ai-yy.com/#pricing',
'TelegramUrl': 'https://t.me/tttt87877',
'GetMachineCodeError': 'Error getting machine code: {0}',
'CreateActivationWindowError': 'Error creating activation window: {0}',
'StartupMessage': 'Let the show begin!',
'ProcessingCoreTitle': 'Processing Core',
'SaveHotkey': 'Save Hotkey',
'TimeLineStart': 'TimeLine Start',
'TimeLineLeft30': 'TimeLine Left 30',
'TimeLineRight30': 'TimeLine Right 30',
'Record': 'Record',
'Play': 'Play',
'SaveImage': 'Save Image',
'AddMark': 'Add Mark',
'DelMark': 'Del Mark',
'PrevMark': 'Prev Mark',
'NextMark': 'Next Mark',
'ToggleRestore': 'Toggle Restore',
'ToggleRestore2': 'Toggle Restore 2',
'ToggleDirection': 'Toggle Direction',
'ToggleStrength': 'Toggle Strength',
'ToggleDiff': 'Toggle Diff',
'ToggleOccluder': 'Toggle Occluder',
'ToggleFaceParser': 'Toggle Face Parser',
'ToggleCLIP': 'Toggle CLIP',
'ToggleColorAdjustment': 'Toggle Color Adjustment',
'ToggleFaceAdjustment': 'Toggle Face Adjustment',
'ClearMem': 'Clear Mem',
'SwapFaces': 'Swap Faces',
'TimeLineLeft1': 'TimeLine Left 1',
'TimeLineRight1': 'TimeLine Right 1',
'MaskView': 'Mask View',
'UpdateNDIWebcamConfigError': 'Update NDI Webcam config failed: {0}',  
'UpdateOBSWebSocketConfigError': 'Update OBS WebSocket config failed: {0}',  
'ExtendCameras': 'Extending cameras, please wait...',
'ExtendCamerasSuccess': 'Extended cameras!',
'Success': 'Success',
'ExtendCamerasError': 'Failed to extend cameras.',
'CameraExtensionPathInvalid': 'Camera extension path invalid',
'InvalidAppPath': 'Invalid application path: {0}',
'TerminateProcessError': 'Terminate {0} process failed: {1}',
'StartApplicationError': 'Start {0} failed: {1}',
'AccessDenied': 'Cannot access {0} process information: {1}',
'OBSPathNotFound': 'OBS installation path not found',
'OBSWebSocketPluginConfigNotFound': 'OBS WebSocket plugin config file not found',
'ImageResourceLoadFailed': 'Image resource load failed, please check the program integrity',
'ActivationFailed': 'Software not activated or license invalid.',
'ErrorRunningCoordinator': 'Error running program: {0}',
'TkinterError': 'Tkinter error: {0}',
'MachineCode': 'Machine Code',
'Permanent': 'Permanent',
'1day': '1 day',
'3days': '3 days',
'1week': '1 week',
'1month': '1 month',
'3months': '3 months',
'6months': '6 months',
'1year': '1 year',
'Save': 'Save',
'Apply': 'Apply',
'Load': 'Load',
'Default': 'Default',
'RTMPSnifferInfoText': 'Please start live streaming and prepare to capture data packets...',
'ConfigFileNotFound': 'Configuration file not found',
'Packets': 'Packets',
'ConfigFileFormatError': 'Configuration file format error',
'StartRTMPSniffer': 'Start RTMP Sniffer...',
'UsedInterface': 'Used Interface',
'UsedDisplayFilter': 'Used Display Filter',
'UsedTsharkPath': 'Used Tshark Path',
'TimeConsumed': 'Time Consumed',
'Seconds': 'Seconds',
'Found': 'Found',
'RTMPInfo': 'RTMP Info',
'Server': 'Server',
'StreamKey': 'Stream Key',
'SuccessRTMPInfo': 'Successfully captured RTMP information:',
'OnlyServerInfo': 'Only server information captured:',
'NoStreamKeyInfo': 'No stream key information captured.',
'NoRTMPInfo': 'No RTMP information captured. Please ensure the live streaming application is open and try starting the live stream again.',
'LaunchError': 'Launch error',
'LaunchErrorMsg': 'Please use the launcher to run the program',
'LoadLicenseInfoError': 'Load license info error: {0}',
'SaveLicenseInfoError': 'Save license info error: {0}',
'NoLicenseInfo': 'No license info',
'IncompleteLicenseInfo': 'Incomplete license info',
'LicenseMachineIDMismatch': 'License machine ID mismatch',
'LicenseExpired': 'License expired',
'LicenseVerificationFailed': 'License verification failed',
'LicenseVerificationFailedMsg': 'Online verification failed, using local verification',
'LicenseSignatureInvalid': 'License signature invalid',
'LicenseValid': 'License valid',
'CheckLicenseError': 'Check license error: {0}',
'LaunchTimeout': 'Launch timeout',
'LicenseInvalid': 'License invalid',
'LicenseActivated': 'Activation successful',
'RegisterSoftware': 'Register Software',
'RestartText': 'Click OK to restart the application.',
'LicenseExpired': 'License expired',
'LicenseUsedOnOtherMachine': 'This activation code has been used on another device',
'LicenseActivationError': 'Activation failed: The license is invalid.',
'JoinGroup': 'Join Group',
'GroupUrl': 'https://t.me/+n7Mda_NATtBlMmJh',
'TelegramText': 'Telegram @tttt87877',
'QQText': 'QQ: **********',
'OBSNotReady': 'OBS is not ready',
'EmptyActivationCode': 'Please enter the activation code.',
'ExpiredOn': 'Expired on {}',
'ExpiringToday': 'Expires on {}',
'ExpiresOn': 'Expires on {}',
'LicenseVersionMismatch': 'This activation code is not valid for the current version',
'HelpUrl': 'https://docs.ai-yy.com',
}

PARAM_VARS =    {

    'CLIPState':                False,
    'CLIPMode':                 0,
    'CLIPModes':                ['CLIP'],
    'CLIPAmount':               [50],
    'CLIPMin':                  0,
    'CLIPMax':                  100,
    'CLIPInc':                  1,
    'CLIPUnit':                 '%',
    'CLIPIcon':                 get_media_path('CLIP.png'),
    'CLIPMessage':              'CLIP - Text based occluder. Occluded objects are visible in the final image (occluded from the mask). [LB: on/off, MW: strength]',
    'CLIPFunction':         False,

    "CLIPText":                 '',
}

PARAMS =   {

    'ClearmemFunction':         'self.clear_mem()',
    'PerfTestFunction':         'self.toggle_perf_test()',
    'ImgVidFunction':         'self.toggle_vid_img()',
    'AutoSwapFunction':         'self.toggle_auto_swap()',
    'SaveImageFunction':         'self.save_image()',

    'ClearmemIcon':            get_media_path('clear_mem.png'),
    'SaveImageIcon':            get_media_path('save_disk.png'),
    'PerfTestIcon':            get_media_path('test.png'),
    'RefDelIcon':          get_media_path('construction.png'),
    'TransformIcon':          get_media_path('scale.png'),
    'ThresholdIcon':            get_media_path('thresh.png'),
    'LoadSFacesIcon':            get_media_path('save.png'),
    'BorderIcon':                 get_media_path('maskup.png'),
    'OccluderIcon':             get_media_path('occluder.png'),
    'ColorIcon':            get_media_path('rgb.png'),
    'StrengthIcon':             get_media_path('strength.png'),
    'OrientationIcon':          get_media_path('orient.png'),
    'DiffIcon':                 get_media_path('diff.png'),
    'MouthParserIcon':           get_media_path('parse.png'),
    'AudioIcon':            get_media_path('rgb.png'),
    'VideoQualityIcon':            get_media_path('tarface.png'),
    'MaskViewIcon':             get_media_path('maskblur.png'),
    'CompareViewIcon':          get_media_path('maskblur.png'),
    'BlurIcon':                 get_media_path('blur.png'),
    'ToggleStopIcon':            get_media_path('STOP.png'),
     'DelEmbedIcon':            get_media_path('delemb.png'),
    'ImgVidIcon':            get_media_path('imgvid.png'),

    'ImgVidMessage':         'IMAGE/VIDEO - Toggle between Image and Video folder view.',
    'ToggleStopMessage':         'STOP MARKER - Sets a frame that will stop the video playing/recording.',
    'AutoSwapMessage':         'AUTO SWAP - Automatically swaps the first person in an image to the selcted source faces [LB: Turn on/off]',
    'SaveImageMessage':         'SAVE IMAGE - Save image to output folder',
    'ClearmemMessage':         'CLEAR VRAM - Clears all models from VRAM [LB: Clear]',
    'PerfTestMessage':         'PERFORMANCE DATA - Displays timing data in the console for critical StarPluck functions. [LB: on/off]',
    'RefDelMessage':       'REFERENCE DELTA - Modify the reference points. Turn on mask preview to see adjustments. [LB: on/off, RB: translate x/y, and scale, MW: amount]' ,
    'ThresholdMessage':         'THRESHOLD - Threshold for determining if Target Faces match faces in a frame. Lower is stricter. [LB: use amount/match all, MW: value]',
    'TransformMessage':       'SCALE - Adjust the scale of the face. Use with Background parser to blend into the image. [LB: on/off, MW: amount]',
    'PlayMessage':         'PLAY - Plays the video. Press again to stop playing',

     }

CAMERA_BACKENDS = {
    'Default': cv2.CAP_ANY,
    'DirectShow': cv2.CAP_DSHOW,
    'MSMF': cv2.CAP_MSMF,
    'V4L': cv2.CAP_V4L,
    'V4L2': cv2.CAP_V4L2,
    'GSTREAMER': cv2.CAP_GSTREAMER,
}

