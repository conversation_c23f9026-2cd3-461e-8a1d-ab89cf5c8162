#!/usr/bin/env python3
"""
修复 'list' object has no attribute 'io_binding' 错误
"""

import sys
import os
import datetime
import numpy as np

def setup_environment():
    """设置环境变量"""
    os.environ['ROPE_SESSION'] = 'fix_io_binding_' + str(int(datetime.datetime.now().timestamp()))
    os.environ['ROPE_LAUNCH_TIME'] = datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")

def test_parameter_types():
    """测试参数类型处理"""
    try:
        print("测试参数类型处理...")
        
        from rope.safe_swap_integration import SafeSwapIntegration
        from rope.enhanced_face_swap_v2 import EnhancedFaceSwapperV2, EnhancedSwapConfigV2
        
        # 创建测试数据
        test_img = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        test_landmarks_list = [[100, 150], [200, 150], [150, 200], [120, 250], [180, 250]]
        test_landmarks_array = np.array(test_landmarks_list)
        
        print(f"✓ 测试图像形状: {test_img.shape}")
        print(f"✓ 测试关键点(list): {len(test_landmarks_list)} 点")
        print(f"✓ 测试关键点(array): {test_landmarks_array.shape}")
        
        # 测试增强处理器
        config = EnhancedSwapConfigV2()
        processor = EnhancedFaceSwapperV2(config)
        
        # 测试list类型的landmarks
        try:
            result = processor.process_face_swap(
                target_img=test_img,
                swapped_face=test_img.copy(),
                face_landmarks=test_landmarks_list,  # 故意传入list
                quality_mode='normal'
            )
            print("✓ list类型landmarks处理成功")
        except Exception as e:
            print(f"❌ list类型landmarks处理失败: {e}")
        
        # 测试array类型的landmarks
        try:
            result = processor.process_face_swap(
                target_img=test_img,
                swapped_face=test_img.copy(),
                face_landmarks=test_landmarks_array,  # numpy数组
                quality_mode='normal'
            )
            print("✓ array类型landmarks处理成功")
        except Exception as e:
            print(f"❌ array类型landmarks处理失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 参数类型测试失败: {e}")
        return False

def test_safe_integration():
    """测试安全集成"""
    try:
        print("测试安全集成...")
        
        from rope.safe_swap_integration import SafeSwapIntegration
        
        # 创建模拟VideoManager
        class MockVideoManager:
            def __init__(self):
                self.swap_core = self.mock_swap_core
            
            def mock_swap_core(self, img, kps_5, kps, s_e, t_e, dfl_model, parameters, control):
                # 模拟返回numpy数组
                if hasattr(img, 'permute'):
                    return img.permute(1, 2, 0).cpu().numpy()
                else:
                    return img
        
        vm = MockVideoManager()
        integration = SafeSwapIntegration(vm)
        
        # 测试数据
        test_img = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        test_kps_5 = [[100, 150], [200, 150], [150, 200], [120, 250], [180, 250]]  # list类型
        test_kps = np.random.rand(68, 2) * 100
        test_s_e = np.random.rand(512)
        test_t_e = np.random.rand(512)
        
        test_parameters = {
            'QualityModeTextSel': '标准',
            'UseEnhancedSwapSwitch': True
        }
        test_control = {'SwapFacesButton': True}
        
        # 测试增强包装器
        try:
            result = integration.enhanced_swap_wrapper(
                test_img, test_kps_5, test_kps, test_s_e, test_t_e, 
                False, test_parameters, test_control
            )
            print("✓ 增强包装器测试成功")
            print(f"  - 结果类型: {type(result)}")
            if isinstance(result, np.ndarray):
                print(f"  - 结果形状: {result.shape}")
        except Exception as e:
            print(f"❌ 增强包装器测试失败: {e}")
            import traceback
            print(f"  - 错误详情: {traceback.format_exc()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 安全集成测试失败: {e}")
        return False

def create_debug_config():
    """创建调试配置"""
    try:
        print("创建调试配置...")
        
        import json
        
        debug_config = {
            "UseEnhancedSwap": True,
            "QualityMode": "normal",
            "enable_debug_logging": True,
            "enable_parameter_validation": True,
            "enable_type_checking": True,
            "fallback_on_error": True,
            "blend_strength": 0.8,
            "enable_color_matching": True,
            "enable_face_enhancement": True,
            "enable_skin_smoothing": True,
            "skin_smooth_strength": 0.3
        }
        
        os.makedirs("config", exist_ok=True)
        with open("config/debug_io_binding.json", 'w', encoding='utf-8') as f:
            json.dump(debug_config, f, indent=2)
        
        print("✓ 调试配置已创建: config/debug_io_binding.json")
        return True
        
    except Exception as e:
        print(f"❌ 创建调试配置失败: {e}")
        return False

def create_error_fix_guide():
    """创建错误修复指南"""
    try:
        print("创建错误修复指南...")
        
        guide = """
# 'list' object has no attribute 'io_binding' 错误修复指南

## 🔍 错误原因

这个错误通常发生在以下情况：
1. 传递给增强处理器的参数类型不正确
2. face_landmarks参数是list而不是numpy数组
3. 图像数据类型转换问题

## 🛠️ 已修复的问题

### 1. 参数类型验证
- 增加了严格的参数类型检查
- 自动转换list类型的landmarks为numpy数组
- 验证数组形状和维度

### 2. 安全的类型转换
```python
# 自动处理list类型的landmarks
if isinstance(face_landmarks, list):
    face_landmarks = np.array(face_landmarks)
```

### 3. 错误恢复机制
- 参数验证失败时自动回退
- 详细的错误日志记录
- 保证程序不会崩溃

## 🎯 测试步骤

### 1. 重启程序
```bash
.\venv3.10\python.exe Rope.py
```

### 2. 测试质量模式
1. 选择"标准"模式
2. 启用换脸功能
3. 观察控制台输出

### 3. 预期结果
- 不再出现 'io_binding' 错误
- 增强功能正常工作
- 质量模式切换有效果

## 📊 控制台输出

### 正常情况
```
✓ 安全增强换脸功能已集成
增强换脸处理器V2初始化完成 - 质量模式: normal
```

### 如果仍有问题
```
增强处理失败，返回原始结果: [具体错误信息]
增强处理失败详情 - 参数类型: [参数类型信息]
```

## 🔧 进一步调试

### 1. 启用详细日志
编辑 config/debug_io_binding.json：
```json
{
  "enable_debug_logging": true,
  "enable_parameter_validation": true,
  "enable_type_checking": true
}
```

### 2. 检查参数类型
运行诊断脚本：
```bash
.\venv3.10\python.exe fix_io_binding_error.py
```

### 3. 查看错误堆栈
观察控制台的详细错误信息，包括完整的错误堆栈。

## 💡 预防措施

### 1. 确保参数正确
- face_landmarks应该是numpy数组
- 图像数据应该是uint8类型
- 形状应该是(height, width, 3)

### 2. 使用安全模式
如果问题持续，可以：
- 关闭"启用增强换脸"开关
- 使用"快速"质量模式
- 检查源图像质量

## 📞 技术支持

如果问题仍然存在：
1. 提供完整的控制台日志
2. 描述具体的操作步骤
3. 说明使用的图像类型和分辨率
"""
        
        with open("io_binding错误修复指南.md", 'w', encoding='utf-8') as f:
            f.write(guide)
        
        print("✓ 错误修复指南已创建")
        return True
        
    except Exception as e:
        print(f"❌ 创建错误修复指南失败: {e}")
        return False

def show_fix_summary():
    """显示修复总结"""
    print("\n" + "="*60)
    print("🔧 io_binding错误修复总结")
    print("="*60)
    
    print("\n📋 已修复的问题:")
    print("  ✓ 参数类型验证和转换")
    print("  ✓ list类型landmarks自动转换为numpy数组")
    print("  ✓ 图像数据类型安全处理")
    print("  ✓ 增强的错误恢复机制")
    
    print("\n🛠️ 修复内容:")
    print("  • 严格的参数类型检查")
    print("  • 自动类型转换")
    print("  • 详细的错误日志")
    print("  • 安全的回退机制")
    
    print("\n🎯 预期效果:")
    print("  • 不再出现 'io_binding' 错误")
    print("  • 增强功能正常工作")
    print("  • 质量模式切换有明显效果")
    
    print("\n🚀 下一步:")
    print("  1. 重启程序")
    print("  2. 测试质量模式切换")
    print("  3. 观察控制台输出")
    print("  4. 参考错误修复指南")

def main():
    """主函数"""
    print("=" * 60)
    print("Rope Live Stellar - 修复 io_binding 错误")
    print("=" * 60)
    
    # 设置环境
    setup_environment()
    
    fixes = [
        ("测试参数类型处理", test_parameter_types),
        ("测试安全集成", test_safe_integration),
        ("创建调试配置", create_debug_config),
        ("创建错误修复指南", create_error_fix_guide),
    ]
    
    completed = 0
    total = len(fixes)
    
    for fix_name, fix_func in fixes:
        print(f"\n--- {fix_name} ---")
        try:
            if fix_func():
                completed += 1
                print(f"✅ {fix_name} 完成")
            else:
                print(f"⚠️ {fix_name} 部分完成")
        except Exception as e:
            print(f"❌ {fix_name} 失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"修复结果: {completed}/{total} 完成")
    
    if completed >= 3:  # 至少完成主要修复
        print("🎉 io_binding错误已修复！")
        show_fix_summary()
        
        return True
    else:
        print("⚠️ 部分修复失败，请检查错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
