#!/usr/bin/env python3
"""
摄像头换脸测试脚本
"""

import cv2
import numpy as np
import time

def test_camera():
    """测试摄像头基础功能"""
    print("测试摄像头...")
    
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("❌ 无法打开摄像头")
        return False
    
    print("✓ 摄像头打开成功")
    
    # 测试读取帧
    for i in range(10):
        ret, frame = cap.read()
        if not ret:
            print(f"❌ 读取第{i+1}帧失败")
            cap.release()
            return False
        
        print(f"✓ 第{i+1}帧读取成功 - 尺寸: {frame.shape}")
        time.sleep(0.1)
    
    cap.release()
    print("✓ 摄像头测试完成")
    return True

def test_image_processing():
    """测试图像处理"""
    print("测试图像处理...")
    
    # 创建测试图像
    test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    print(f"✓ 测试图像创建成功 - 尺寸: {test_image.shape}")
    
    # 测试基础处理
    try:
        # 颜色空间转换
        rgb_image = cv2.cvtColor(test_image, cv2.COLOR_BGR2RGB)
        print("✓ 颜色空间转换成功")
        
        # 尺寸调整
        resized = cv2.resize(rgb_image, (512, 512))
        print("✓ 图像缩放成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 图像处理失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("摄像头换脸测试")
    print("=" * 50)
    
    tests = [
        ("摄像头基础功能", test_camera),
        ("图像处理功能", test_image_processing),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 摄像头功能正常！")
        return True
    else:
        print("⚠️ 部分功能异常，请检查硬件连接。")
        return False

if __name__ == "__main__":
    success = main()
    input("按任意键退出...")
