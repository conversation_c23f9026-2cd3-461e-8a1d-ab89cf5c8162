"""
资源管理系统
"""
import atexit
import gc
import threading
import weakref
from contextlib import contextmanager
from typing import List, Callable, Any, Optional, Dict, Union
from pathlib import Path
import subprocess
import time

# 尝试导入可选依赖
try:
    import torch
    HAS_TORCH = True
except ImportError:
    HAS_TORCH = False
    torch = None

try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False
    psutil = None

from .logger import get_logger
from .error_handler import handle_error, ErrorSeverity, ErrorCategory

class ResourceType:
    """资源类型常量"""
    GPU_MEMORY = "gpu_memory"
    SYSTEM_MEMORY = "system_memory"
    FILE_HANDLE = "file_handle"
    PROCESS = "process"
    THREAD = "thread"
    MODEL = "model"
    TENSOR = "tensor"

class ResourceManager:
    """统一资源管理器"""
    
    def __init__(self):
        self.logger = get_logger("ResourceManager")
        self.cleanup_functions: List[Callable] = []
        self.managed_resources: Dict[str, Any] = {}
        self.resource_refs: List[weakref.ref] = []
        self._lock = threading.RLock()
        self._shutdown = False
        
        # 注册程序退出时的清理
        atexit.register(self.cleanup_all)
        
        # 启动资源监控线程
        self._start_monitoring()
    
    def register_cleanup(self, cleanup_func: Callable, priority: int = 0):
        """
        注册清理函数
        
        Args:
            cleanup_func: 清理函数
            priority: 优先级，数字越大优先级越高
        """
        with self._lock:
            self.cleanup_functions.append((priority, cleanup_func))
            # 按优先级排序
            self.cleanup_functions.sort(key=lambda x: x[0], reverse=True)
    
    def register_resource(self, resource: Any, resource_id: str = None, 
                         cleanup_func: Optional[Callable] = None):
        """
        注册需要管理的资源
        
        Args:
            resource: 资源对象
            resource_id: 资源ID
            cleanup_func: 自定义清理函数
        """
        with self._lock:
            if resource_id:
                self.managed_resources[resource_id] = resource
            
            # 使用弱引用避免循环引用
            if cleanup_func:
                def cleanup_wrapper(ref):
                    try:
                        cleanup_func()
                    except Exception as e:
                        self.logger.error(f"资源清理失败: {e}")
                
                weak_ref = weakref.ref(resource, cleanup_wrapper)
                self.resource_refs.append(weak_ref)
    
    def unregister_resource(self, resource_id: str):
        """注销资源"""
        with self._lock:
            if resource_id in self.managed_resources:
                del self.managed_resources[resource_id]
    
    @contextmanager
    def managed_resource(self, resource: Any, cleanup_func: Optional[Callable] = None):
        """
        资源管理上下文管理器
        
        Args:
            resource: 资源对象
            cleanup_func: 清理函数
        """
        try:
            if cleanup_func:
                self.register_cleanup(cleanup_func)
            yield resource
        finally:
            if cleanup_func:
                try:
                    cleanup_func()
                except Exception as e:
                    handle_error(e, ErrorSeverity.MEDIUM, ErrorCategory.SYSTEM, 
                                "资源清理失败")
    
    @contextmanager
    def gpu_memory_context(self, clear_cache: bool = True):
        """GPU内存管理上下文"""
        initial_memory = 0
        if HAS_TORCH and torch.cuda.is_available():
            initial_memory = torch.cuda.memory_allocated()

        try:
            yield
        finally:
            if HAS_TORCH and torch.cuda.is_available() and clear_cache:
                torch.cuda.empty_cache()
                final_memory = torch.cuda.memory_allocated()
                freed_memory = (initial_memory - final_memory) / 1024**2  # MB
                if freed_memory > 0:
                    self.logger.debug(f"释放GPU内存: {freed_memory:.2f}MB")
    
    def cleanup_all(self):
        """清理所有资源"""
        if self._shutdown:
            return
        
        self._shutdown = True
        self.logger.info("开始清理所有资源...")
        
        with self._lock:
            # 执行注册的清理函数
            for priority, cleanup_func in self.cleanup_functions:
                try:
                    cleanup_func()
                except Exception as e:
                    self.logger.error(f"清理函数执行失败 (优先级 {priority}): {e}")
            
            # 清理GPU内存
            self._cleanup_gpu_memory()
            
            # 清理系统内存
            self._cleanup_system_memory()
            
            # 清理进程
            self._cleanup_processes()
        
        self.logger.info("资源清理完成")
    
    def _cleanup_gpu_memory(self):
        """清理GPU内存"""
        try:
            if HAS_TORCH and torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                self.logger.info("GPU内存清理完成")
            else:
                self.logger.debug("torch未安装或CUDA不可用，跳过GPU内存清理")
        except Exception as e:
            self.logger.error(f"GPU内存清理失败: {e}")
    
    def _cleanup_system_memory(self):
        """清理系统内存"""
        try:
            gc.collect()
            self.logger.info("系统内存清理完成")
        except Exception as e:
            self.logger.error(f"系统内存清理失败: {e}")
    
    def _cleanup_processes(self):
        """清理相关进程"""
        processes_to_kill = ['obs64.exe', 'ffmpeg.exe']
        
        for process_name in processes_to_kill:
            try:
                subprocess.run(
                    ['taskkill', '/F', '/IM', process_name],
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    timeout=5
                )
                self.logger.debug(f"已终止进程: {process_name}")
            except subprocess.TimeoutExpired:
                self.logger.warning(f"终止进程超时: {process_name}")
            except Exception as e:
                self.logger.debug(f"终止进程失败 {process_name}: {e}")
    
    def _start_monitoring(self):
        """启动资源监控"""
        def monitor():
            while not self._shutdown:
                try:
                    self._check_memory_usage()
                    time.sleep(30)  # 每30秒检查一次
                except Exception as e:
                    self.logger.error(f"资源监控错误: {e}")
                    time.sleep(60)  # 出错后等待更长时间
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def _check_memory_usage(self):
        """检查内存使用情况"""
        try:
            # 检查系统内存
            if HAS_PSUTIL:
                memory = psutil.virtual_memory()
                if memory.percent > 90:
                    self.logger.warning(f"系统内存使用率过高: {memory.percent}%")
                    gc.collect()

            # 检查GPU内存
            if HAS_TORCH and torch.cuda.is_available():
                for i in range(torch.cuda.device_count()):
                    allocated = torch.cuda.memory_allocated(i)
                    total = torch.cuda.get_device_properties(i).total_memory
                    usage_percent = (allocated / total) * 100

                    if usage_percent > 90:
                        self.logger.warning(f"GPU {i} 内存使用率过高: {usage_percent:.1f}%")
                        torch.cuda.empty_cache()

        except Exception as e:
            self.logger.error(f"内存检查失败: {e}")
    
    def get_resource_stats(self) -> Dict[str, Any]:
        """获取资源使用统计"""
        stats = {}
        
        try:
            # 系统内存
            if HAS_PSUTIL:
                memory = psutil.virtual_memory()
                stats['system_memory'] = {
                    'total_gb': memory.total / 1024**3,
                    'used_gb': memory.used / 1024**3,
                    'available_gb': memory.available / 1024**3,
                    'percent': memory.percent
                }
            else:
                stats['system_memory'] = {'error': 'psutil not available'}

            # GPU内存
            if HAS_TORCH and torch.cuda.is_available():
                gpu_stats = []
                for i in range(torch.cuda.device_count()):
                    allocated = torch.cuda.memory_allocated(i) / 1024**3
                    cached = torch.cuda.memory_reserved(i) / 1024**3
                    total = torch.cuda.get_device_properties(i).total_memory / 1024**3

                    gpu_stats.append({
                        'device': i,
                        'allocated_gb': allocated,
                        'cached_gb': cached,
                        'total_gb': total,
                        'percent': (allocated / total) * 100
                    })

                stats['gpu_memory'] = gpu_stats
            else:
                stats['gpu_memory'] = {'error': 'torch not available or CUDA not available'}
            
            # 管理的资源数量
            stats['managed_resources'] = len(self.managed_resources)
            stats['cleanup_functions'] = len(self.cleanup_functions)
            
        except Exception as e:
            self.logger.error(f"获取资源统计失败: {e}")
            stats['error'] = str(e)
        
        return stats

class TensorPool:
    """张量对象池，减少GPU内存分配"""

    def __init__(self, pool_size: int = 10, device: str = 'cuda'):
        self.pool_size = pool_size
        self.device = device
        self.pools: Dict[tuple, List] = {}
        self._lock = threading.Lock()
        self.logger = get_logger("TensorPool")
        self.enabled = HAS_TORCH

        if not self.enabled:
            self.logger.warning("torch未安装，TensorPool功能禁用")

    def get_tensor(self, shape: tuple, dtype=None):
        """从池中获取张量"""
        if not self.enabled:
            return None

        if dtype is None:
            dtype = torch.float32

        key = (shape, dtype)

        with self._lock:
            if key in self.pools and self.pools[key]:
                tensor = self.pools[key].pop()
                tensor.zero_()  # 清零但保留内存
                return tensor

            # 池中没有可用张量，创建新的
            return torch.zeros(shape, dtype=dtype, device=self.device)
    
    def return_tensor(self, tensor):
        """将张量返回到池中"""
        if not self.enabled or tensor is None:
            return

        if tensor.device.type != self.device.split(':')[0]:
            return  # 设备不匹配，不回收

        key = (tuple(tensor.shape), tensor.dtype)

        with self._lock:
            if key not in self.pools:
                self.pools[key] = []

            if len(self.pools[key]) < self.pool_size:
                self.pools[key].append(tensor)

    def clear_pool(self):
        """清空池"""
        with self._lock:
            self.pools.clear()
            if self.enabled and torch.cuda.is_available():
                torch.cuda.empty_cache()

# 全局资源管理器
resource_manager = ResourceManager()

# 全局张量池
tensor_pool = TensorPool()

# 便捷函数
def cleanup_gpu_memory():
    """清理GPU内存的便捷函数"""
    if HAS_TORCH and torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()
        gc.collect()

def get_memory_usage() -> Dict[str, float]:
    """获取内存使用情况"""
    usage = {}

    # 系统内存
    if HAS_PSUTIL:
        memory = psutil.virtual_memory()
        usage['system_memory_percent'] = memory.percent
        usage['system_memory_gb'] = memory.used / 1024**3

    # GPU内存
    if HAS_TORCH and torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            allocated = torch.cuda.memory_allocated(i) / 1024**3
            total = torch.cuda.get_device_properties(i).total_memory / 1024**3
            usage[f'gpu_{i}_memory_gb'] = allocated
            usage[f'gpu_{i}_memory_percent'] = (allocated / total) * 100

    return usage
