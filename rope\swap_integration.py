"""
增强换脸功能集成模块
将新的增强算法集成到现有的VideoManager中
"""

import torch
import numpy as np
from typing import Dict, Any, Optional
import time

from .enhanced_face_swap import EnhancedFaceSwapper, EnhancedSwapConfig, SwapOptimizer
from .logger import get_logger, LogExecutionTime
from .config import config_manager

class SwapIntegration:
    """换脸功能集成器"""
    
    def __init__(self, video_manager):
        self.vm = video_manager
        self.logger = get_logger("SwapIntegration")
        
        # 加载配置
        self.config = self._load_enhanced_config()
        
        # 初始化增强换脸器
        self.enhanced_swapper = EnhancedFaceSwapper(self.config)
        
        # 性能统计
        self.performance_stats = {
            'total_swaps': 0,
            'enhanced_swaps': 0,
            'avg_processing_time': 0.0,
            'quality_improvements': 0
        }
        
        self.logger.info("增强换脸功能已初始化")
    
    def _load_enhanced_config(self) -> EnhancedSwapConfig:
        """加载增强配置"""
        try:
            processing_config = config_manager.get_config('processing')
            
            return EnhancedSwapConfig(
                target_resolution=getattr(processing_config, 'target_resolution', 512),
                upscale_factor=getattr(processing_config, 'upscale_factor', 2.0),
                enable_super_resolution=getattr(processing_config, 'enable_super_resolution', True),
                blend_method=getattr(processing_config, 'blend_method', 'poisson'),
                blend_strength=getattr(processing_config, 'blend_strength', 0.8),
                feather_amount=getattr(processing_config, 'feather_amount', 15),
                enable_color_matching=getattr(processing_config, 'enable_color_matching', True),
                color_transfer_method=getattr(processing_config, 'color_transfer_method', 'histogram'),
                enable_face_enhancement=getattr(processing_config, 'enable_face_enhancement', True),
                enable_skin_smoothing=getattr(processing_config, 'enable_skin_smoothing', True),
                skin_smooth_strength=getattr(processing_config, 'skin_smooth_strength', 0.3),
                use_half_precision=getattr(processing_config, 'use_half_precision', True),
                enable_tensorrt=getattr(processing_config, 'enable_tensorrt', True),
                batch_processing=getattr(processing_config, 'batch_processing', True)
            )
        except Exception as e:
            self.logger.warning(f"加载增强配置失败，使用默认配置: {e}")
            return EnhancedSwapConfig()
    
    def enhanced_swap_core(self, img, kps_5, kps, s_e, t_e, dfl_model, parameters, control):
        """
        增强的换脸核心函数，替代原始的swap_core
        
        Args:
            img: 输入图像 (torch.Tensor)
            kps_5: 5点关键点
            kps: 完整关键点
            s_e: 源人脸特征
            t_e: 目标人脸特征
            dfl_model: DFL模型
            parameters: 参数字典
            control: 控制字典
            
        Returns:
            处理后的图像
        """
        start_time = time.time()
        
        try:
            # 检查是否启用增强模式
            use_enhanced = parameters.get('UseEnhancedSwap', True)
            
            if use_enhanced and self._should_use_enhanced_swap(img, parameters):
                # 使用增强换脸
                result = self._process_enhanced_swap(
                    img, kps_5, kps, s_e, t_e, dfl_model, parameters, control
                )
                self.performance_stats['enhanced_swaps'] += 1
                self.logger.debug("使用增强换脸处理")
            else:
                # 使用原始换脸
                result = self._process_original_swap(
                    img, kps_5, kps, s_e, t_e, dfl_model, parameters, control
                )
                self.logger.debug("使用原始换脸处理")
            
            # 更新性能统计
            processing_time = time.time() - start_time
            self.performance_stats['total_swaps'] += 1
            self.performance_stats['avg_processing_time'] = (
                self.performance_stats['avg_processing_time'] * 
                (self.performance_stats['total_swaps'] - 1) + processing_time
            ) / self.performance_stats['total_swaps']
            
            return result
            
        except Exception as e:
            self.logger.error(f"增强换脸处理失败: {e}")
            # 回退到原始方法
            return self._process_original_swap(
                img, kps_5, kps, s_e, t_e, dfl_model, parameters, control
            )
    
    def _should_use_enhanced_swap(self, img: torch.Tensor, parameters: Dict[str, Any]) -> bool:
        """判断是否应该使用增强换脸"""
        # 基于图像质量和用户设置决定
        
        # 检查图像分辨率
        height, width = img.shape[1], img.shape[2]
        min_resolution = 256
        
        if height < min_resolution or width < min_resolution:
            return False
        
        # 检查质量设置
        quality_mode = parameters.get('QualityMode', 'high')
        if quality_mode == 'fast':
            return False
        
        # 检查GPU内存
        if torch.cuda.is_available():
            memory_usage = torch.cuda.memory_allocated() / torch.cuda.max_memory_allocated()
            if memory_usage > 0.9:  # GPU内存使用率超过90%
                return False
        
        return True
    
    def _process_enhanced_swap(self, img, kps_5, kps, s_e, t_e, dfl_model, parameters, control):
        """处理增强换脸"""
        with LogExecutionTime("enhanced_swap_processing"):
            # 获取换脸模型
            swapper_model = self._get_swapper_model(parameters)
            
            # 使用增强换脸器
            result = self.enhanced_swapper.enhanced_swap_core(
                img, kps_5, s_e, t_e, swapper_model, parameters
            )
            
            return result
    
    def _process_original_swap(self, img, kps_5, kps, s_e, t_e, dfl_model, parameters, control):
        """处理原始换脸"""
        # 调用原始的swap_core方法（避免递归）
        if hasattr(self.vm, '_original_swap_core'):
            return self.vm._original_swap_core(img, kps_5, kps, s_e, t_e, dfl_model, parameters, control)
        else:
            # 如果没有原始方法，直接返回原图
            self.logger.warning("原始swap_core方法不可用，返回原图")
            if hasattr(img, 'permute'):
                return img.permute(1, 2, 0).cpu().numpy()
            else:
                return img
    
    def _get_swapper_model(self, parameters: Dict[str, Any]):
        """获取换脸模型"""
        model_name = parameters.get('FaceSwapperModelTextSel', 'inswapper_128.fp16.onnx')
        
        # 从VideoManager的models中获取模型
        if hasattr(self.vm, 'models') and hasattr(self.vm.models, 'swapper_model'):
            models = self.vm.models.swapper_model
            if model_name in models:
                return models[model_name]
        
        # 如果没有找到，返回None（会使用默认处理）
        return None
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats = self.performance_stats.copy()
        
        if stats['total_swaps'] > 0:
            stats['enhanced_usage_rate'] = stats['enhanced_swaps'] / stats['total_swaps']
        else:
            stats['enhanced_usage_rate'] = 0.0
        
        return stats
    
    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        try:
            # 更新增强配置
            for key, value in new_config.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
            
            # 重新初始化增强换脸器
            self.enhanced_swapper = EnhancedFaceSwapper(self.config)
            
            self.logger.info("增强换脸配置已更新")
            
        except Exception as e:
            self.logger.error(f"更新配置失败: {e}")

def integrate_enhanced_swap(video_manager):
    """
    将增强换脸功能集成到VideoManager中
    
    Args:
        video_manager: VideoManager实例
    """
    logger = get_logger("SwapIntegration")
    
    try:
        # 创建集成器
        integration = SwapIntegration(video_manager)
        
        # 保存原始的swap_core方法
        video_manager._original_swap_core = video_manager.swap_core
        
        # 替换swap_core方法
        video_manager.swap_core = integration.enhanced_swap_core
        
        # 添加性能统计方法
        video_manager.get_swap_performance_stats = integration.get_performance_stats
        
        # 添加配置更新方法
        video_manager.update_enhanced_swap_config = integration.update_config
        
        logger.info("增强换脸功能已成功集成到VideoManager")
        
        return integration
        
    except Exception as e:
        logger.error(f"集成增强换脸功能失败: {e}")
        return None

def create_enhanced_config_template():
    """创建增强配置模板"""
    return {
        # 质量设置
        "target_resolution": 512,
        "upscale_factor": 2.0,
        "enable_super_resolution": True,
        
        # 混合算法
        "blend_method": "poisson",  # poisson, alpha, seamless
        "blend_strength": 0.8,
        "feather_amount": 15,
        
        # 色彩匹配
        "enable_color_matching": True,
        "color_transfer_method": "histogram",  # histogram, lab, reinhard
        
        # 后处理
        "enable_face_enhancement": True,
        "enable_skin_smoothing": True,
        "skin_smooth_strength": 0.3,
        
        # 性能优化
        "use_half_precision": True,
        "enable_tensorrt": True,
        "batch_processing": True,
        
        # 用户控制
        "UseEnhancedSwap": True,
        "QualityMode": "high"  # fast, normal, high, ultra
    }
