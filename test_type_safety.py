
import numpy as np
import sys
sys.path.append('.')

from rope.safe_swap_integration import safe_tensor_to_numpy

# 测试不同类型的输入
test_cases = [
    ("numpy数组", np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)),
    ("list", [[[255, 0, 0] for _ in range(10)] for _ in range(10)]),
    ("tuple", (1, 2, 3)),
    ("int", 42),
]

for name, test_input in test_cases:
    try:
        result = safe_tensor_to_numpy(test_input)
        print(f"✓ {name}: {type(test_input)} -> {type(result)} {result.shape if hasattr(result, 'shape') else ''}")
    except Exception as e:
        print(f"❌ {name}: {e}")

print("✓ 类型安全测试完成")
