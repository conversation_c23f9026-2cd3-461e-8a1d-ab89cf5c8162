#!/usr/bin/env python3
"""
启用增强换脸功能
这个脚本会将增强的换脸算法集成到现有系统中
"""

import sys
import os
import datetime

def setup_environment():
    """设置环境变量"""
    os.environ['ROPE_SESSION'] = 'enhanced_session_' + str(int(datetime.datetime.now().timestamp()))
    os.environ['ROPE_LAUNCH_TIME'] = datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")

def test_enhanced_swap_import():
    """测试增强换脸模块导入"""
    try:
        print("测试增强换脸模块导入...")
        from rope.enhanced_face_swap import EnhancedFaceSwapper, EnhancedSwapConfig
        from rope.swap_integration import SwapIntegration, integrate_enhanced_swap
        print("✓ 增强换脸模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 增强换脸模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration():
    """测试集成功能"""
    try:
        print("测试集成功能...")
        
        # 模拟VideoManager
        class MockVideoManager:
            def __init__(self):
                self.models = None
            
            def swap_core(self, *args, **kwargs):
                return "original_result"
        
        mock_vm = MockVideoManager()
        
        # 测试集成
        from rope.swap_integration import integrate_enhanced_swap
        integration = integrate_enhanced_swap(mock_vm)
        
        if integration:
            print("✓ 集成功能测试成功")
            
            # 测试性能统计
            stats = integration.get_performance_stats()
            print(f"  - 性能统计: {stats}")
            
            return True
        else:
            print("❌ 集成功能测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 集成功能测试失败: {e}")
        return False

def test_config_loading():
    """测试配置加载"""
    try:
        print("测试配置加载...")
        from rope.config import config_manager
        
        # 加载处理配置
        config = config_manager.get_config('processing')
        
        # 检查增强换脸相关配置
        enhanced_configs = [
            'target_resolution', 'upscale_factor', 'enable_super_resolution',
            'blend_method', 'blend_strength', 'enable_color_matching',
            'enable_face_enhancement', 'enable_skin_smoothing'
        ]
        
        missing_configs = []
        for config_name in enhanced_configs:
            if not hasattr(config, config_name):
                missing_configs.append(config_name)
        
        if missing_configs:
            print(f"⚠️  缺少配置项: {missing_configs}")
            print("  - 将使用默认配置")
        else:
            print("✓ 所有增强配置项都可用")
        
        print(f"  - 目标分辨率: {getattr(config, 'target_resolution', 512)}")
        print(f"  - 上采样倍数: {getattr(config, 'upscale_factor', 2.0)}")
        print(f"  - 混合方法: {getattr(config, 'blend_method', 'poisson')}")
        print(f"  - 启用人脸增强: {getattr(config, 'enable_face_enhancement', True)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载测试失败: {e}")
        return False

def create_enhanced_config():
    """创建增强配置文件"""
    try:
        print("创建增强配置文件...")
        from rope.swap_integration import create_enhanced_config_template
        
        template = create_enhanced_config_template()
        
        # 保存到配置文件
        import json
        from pathlib import Path
        
        config_dir = Path("config")
        config_dir.mkdir(exist_ok=True)
        
        enhanced_config_file = config_dir / "enhanced_swap.json"
        with open(enhanced_config_file, 'w', encoding='utf-8') as f:
            json.dump(template, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 增强配置文件已创建: {enhanced_config_file}")
        print("  - 您可以编辑此文件来调整增强换脸的参数")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建增强配置文件失败: {e}")
        return False

def show_usage_instructions():
    """显示使用说明"""
    print("\n" + "="*60)
    print("🚀 增强换脸功能使用说明")
    print("="*60)
    
    print("\n📋 新增功能:")
    print("  ✓ 高质量人脸对齐算法")
    print("  ✓ 多尺度换脸处理")
    print("  ✓ 智能色彩匹配")
    print("  ✓ 泊松混合算法")
    print("  ✓ 皮肤平滑处理")
    print("  ✓ 细节增强算法")
    print("  ✓ 边缘优化处理")
    
    print("\n⚙️ 配置选项:")
    print("  • target_resolution: 目标分辨率 (默认: 512)")
    print("  • upscale_factor: 上采样倍数 (默认: 2.0)")
    print("  • blend_method: 混合方法 (poisson/alpha/seamless)")
    print("  • enable_color_matching: 启用色彩匹配")
    print("  • enable_face_enhancement: 启用人脸增强")
    print("  • enable_skin_smoothing: 启用皮肤平滑")
    
    print("\n🎯 质量模式:")
    print("  • fast: 快速模式 (使用原始算法)")
    print("  • normal: 标准模式 (部分增强)")
    print("  • high: 高质量模式 (全部增强)")
    print("  • ultra: 超高质量模式 (最大增强)")
    
    print("\n💡 使用建议:")
    print("  1. 首次使用建议使用 'high' 质量模式")
    print("  2. 如果性能不足，可以降低到 'normal' 模式")
    print("  3. 对于高端显卡，可以尝试 'ultra' 模式")
    print("  4. 实时处理时建议使用 'normal' 或 'fast' 模式")
    
    print("\n📁 配置文件位置:")
    print("  • config/processing.json - 主要处理配置")
    print("  • config/enhanced_swap.json - 增强换脸专用配置")

def main():
    """主函数"""
    print("=" * 60)
    print("Rope Live Stellar - 增强换脸功能启用器")
    print("=" * 60)
    
    # 设置环境
    setup_environment()
    
    tests = [
        ("增强换脸模块导入", test_enhanced_swap_import),
        ("集成功能测试", test_integration),
        ("配置加载测试", test_config_loading),
        ("创建增强配置", create_enhanced_config),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 成功")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 增强换脸功能已成功启用！")
        show_usage_instructions()
        
        print("\n🚀 下一步:")
        print("  1. 启动 Rope Live Stellar")
        print("  2. 在设置中启用 '增强换脸模式'")
        print("  3. 选择合适的质量模式")
        print("  4. 享受更高质量的换脸效果！")
        
        return True
    else:
        print("⚠️  部分功能启用失败，但基础功能仍可使用。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
