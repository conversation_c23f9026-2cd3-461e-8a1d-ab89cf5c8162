#!/usr/bin/env python3
"""
测试启动脚本 - 验证依赖修复
"""

import sys
import os
from pathlib import Path

def test_imports():
    """测试所有关键模块的导入"""
    print("测试模块导入...")
    
    try:
        # 测试配置系统
        print("  - 测试配置系统...")
        from rope.config import config_manager, SOFTWARE_VERSION
        print(f"    ✓ 配置系统导入成功，软件版本: {SOFTWARE_VERSION}")
        
        # 测试日志系统
        print("  - 测试日志系统...")
        from rope.logger import get_logger
        logger = get_logger("TestStartup")
        logger.info("日志系统测试成功")
        print("    ✓ 日志系统导入成功")
        
        # 测试错误处理系统
        print("  - 测试错误处理系统...")
        from rope.error_handler import handle_error, ErrorSeverity, ErrorCategory
        print("    ✓ 错误处理系统导入成功")
        
        # 测试资源管理系统
        print("  - 测试资源管理系统...")
        from rope.resource_manager import resource_manager
        print("    ✓ 资源管理系统导入成功")
        
        # 测试事件系统
        print("  - 测试事件系统...")
        from rope.event_system import event_bus, EventType
        print("    ✓ 事件系统导入成功")
        
        # 测试异步处理系统
        print("  - 测试异步处理系统...")
        from rope.async_processor import AsyncVideoProcessor
        print("    ✓ 异步处理系统导入成功")
        
        print("\n✅ 所有新系统模块导入成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ 模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_system():
    """测试配置系统"""
    print("\n测试配置系统功能...")
    
    try:
        from rope.config import config_manager
        
        # 测试配置加载
        processing_config = config_manager.get_config('processing')
        ui_config = config_manager.get_config('ui')
        models_config = config_manager.get_config('models')
        
        print(f"  - 处理配置: {processing_config}")
        print(f"  - UI配置: {ui_config}")
        print(f"  - 模型配置: {models_config}")
        
        print("✅ 配置系统功能测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 配置系统测试失败: {e}")
        return False

def test_logger_system():
    """测试日志系统"""
    print("\n测试日志系统功能...")
    
    try:
        from rope.logger import get_logger, get_performance_logger
        
        # 测试基本日志
        logger = get_logger("TestLogger")
        logger.debug("这是调试信息")
        logger.info("这是信息日志")
        logger.warning("这是警告日志")
        logger.error("这是错误日志")
        
        # 测试性能日志
        perf_logger = get_performance_logger()
        perf_logger.start_timer("test_operation")
        import time
        time.sleep(0.1)
        perf_logger.end_timer("test_operation")
        
        print("✅ 日志系统功能测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 日志系统测试失败: {e}")
        return False

def test_error_handler():
    """测试错误处理系统"""
    print("\n测试错误处理系统功能...")
    
    try:
        from rope.error_handler import handle_error, ErrorSeverity, ErrorCategory
        
        # 测试错误处理
        try:
            raise ValueError("这是一个测试错误")
        except Exception as e:
            result = handle_error(e, ErrorSeverity.LOW, ErrorCategory.SYSTEM, "测试错误处理")
            print(f"  - 错误处理结果: {result}")
        
        print("✅ 错误处理系统功能测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理系统测试失败: {e}")
        return False

def test_resource_manager():
    """测试资源管理系统"""
    print("\n测试资源管理系统功能...")
    
    try:
        from rope.resource_manager import resource_manager, get_memory_usage
        
        # 测试内存使用统计
        memory_usage = get_memory_usage()
        print(f"  - 内存使用情况: {memory_usage}")
        
        # 测试资源统计
        stats = resource_manager.get_resource_stats()
        print(f"  - 资源统计: {stats}")
        
        print("✅ 资源管理系统功能测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 资源管理系统测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("Rope Live Stellar - 启动测试")
    print("=" * 60)
    
    # 设置环境变量（模拟启动器）
    import datetime
    os.environ['ROPE_SESSION'] = 'test_session_12345'
    os.environ['ROPE_LAUNCH_TIME'] = datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")
    
    tests = [
        ("模块导入", test_imports),
        ("配置系统", test_config_system),
        ("日志系统", test_logger_system),
        ("错误处理", test_error_handler),
        ("资源管理", test_resource_manager),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统已准备就绪。")
        return True
    else:
        print("⚠️  部分测试失败，请检查错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
