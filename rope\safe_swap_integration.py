"""
安全的换脸集成模块 V2.0
避免递归调用，提供可选的增强功能
"""

import numpy as np
from typing import Dict, Any, Optional, Callable
import time

from .enhanced_face_swap_v2 import EnhancedFaceSwapperV2, EnhancedSwapConfigV2
from .logger import get_logger


def safe_tensor_to_numpy(tensor_or_array):
    """
    安全地将tensor或其他类型转换为numpy数组
    """
    import numpy as np
    
    try:
        # 如果已经是numpy数组，直接返回
        if isinstance(tensor_or_array, np.ndarray):
            return tensor_or_array
        
        # 如果是PyTorch tensor
        if hasattr(tensor_or_array, 'detach'):
            # 先detach，再转换
            tensor_or_array = tensor_or_array.detach()
        
        if hasattr(tensor_or_array, 'permute'):
            # 如果有permute方法，说明是channels first的tensor
            if len(tensor_or_array.shape) == 3:
                tensor_or_array = tensor_or_array.permute(1, 2, 0)
            elif len(tensor_or_array.shape) == 4:
                tensor_or_array = tensor_or_array.permute(0, 2, 3, 1)
        
        if hasattr(tensor_or_array, 'cpu'):
            tensor_or_array = tensor_or_array.cpu()
        
        if hasattr(tensor_or_array, 'numpy'):
            return tensor_or_array.numpy()
        
        # 尝试直接转换
        return np.array(tensor_or_array)
        
    except Exception as e:
        print(f"类型转换失败: {e}, 类型: {type(tensor_or_array)}")
        # 最后的回退
        if hasattr(tensor_or_array, 'shape'):
            shape = tensor_or_array.shape
            if len(shape) >= 2:
                return np.zeros(shape, dtype=np.uint8)
        
        # 创建默认图像
        return np.zeros((480, 640, 3), dtype=np.uint8)

from .config import config_manager

class SafeSwapIntegration:
    """安全的换脸集成器 - 避免递归调用"""
    
    def __init__(self, video_manager):
        self.vm = video_manager
        self.logger = get_logger("SafeSwapIntegration")
        
        # 加载配置
        self.config = self._load_config()
        
        # 初始化增强处理器
        self.enhancer = None
        if self.config.enabled:
            try:
                self.enhancer = EnhancedFaceSwapperV2(self.config)
                self.logger.info("增强换脸处理器初始化成功")
            except Exception as e:
                self.logger.warning(f"增强换脸处理器初始化失败: {e}")
                self.enhancer = None
        
        # 性能统计
        self.stats = {
            'total_calls': 0,
            'enhanced_calls': 0,
            'fallback_calls': 0,
            'avg_processing_time': 0.0
        }
        
        # 保存原始方法的引用
        self.original_swap_core = None
        
        self.logger.info("安全换脸集成器初始化完成")
    
    def _load_config(self) -> EnhancedSwapConfigV2:
        """加载配置"""
        try:
            # 尝试从配置文件加载
            enhanced_config_file = "config/enhanced_swap.json"
            import json
            from pathlib import Path
            
            if Path(enhanced_config_file).exists():
                with open(enhanced_config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                return EnhancedSwapConfigV2(
                    enabled=config_data.get('UseEnhancedSwap', True),
                    quality_mode=config_data.get('QualityMode', 'normal'),
                    target_resolution=config_data.get('target_resolution', 512),
                    upscale_factor=config_data.get('upscale_factor', 1.5),
                    enable_super_resolution=config_data.get('enable_super_resolution', False),
                    blend_method=config_data.get('blend_method', 'alpha'),
                    blend_strength=config_data.get('blend_strength', 0.85),
                    feather_amount=config_data.get('feather_amount', 12),
                    enable_color_matching=config_data.get('enable_color_matching', True),
                    color_transfer_method=config_data.get('color_transfer_method', 'histogram'),
                    enable_face_enhancement=config_data.get('enable_face_enhancement', True),
                    enable_skin_smoothing=config_data.get('enable_skin_smoothing', True),
                    skin_smooth_strength=config_data.get('skin_smooth_strength', 0.25),
                    use_half_precision=config_data.get('use_half_precision', True),
                    enable_caching=config_data.get('enable_caching', True),
                    batch_processing=config_data.get('batch_processing', False)
                )
            else:
                self.logger.info("使用默认增强配置")
                return EnhancedSwapConfigV2()
                
        except Exception as e:
            self.logger.warning(f"加载增强配置失败，使用默认配置: {e}")
            return EnhancedSwapConfigV2()
    
    def enhanced_swap_wrapper(self, img, kps_5, kps, s_e, t_e, dfl_model, parameters, control):
        """
        增强换脸包装器 - 不会产生递归调用
        
        这个方法会在原始swap_core处理完成后，对结果进行增强处理
        """
        start_time = time.time()
        
        # 调试日志
        self.logger.debug(f"输入参数类型: img={type(img)}, kps_5={type(kps_5)}")
        
        try:
            # 1. 首先调用原始的swap_core方法
            if self.original_swap_core is not None:
                # 调用保存的原始方法
                swapped_result = self.original_swap_core(img, kps_5, kps, s_e, t_e, dfl_model, parameters, control)

                # 调试：检查原始换脸是否工作
                if isinstance(img, np.ndarray) and isinstance(swapped_result, np.ndarray):
                    original_diff = np.mean(np.abs(img.astype(float) - swapped_result.astype(float)))
                    self.logger.debug(f"原始换脸效果检查: 差异={original_diff:.3f}")
                    if original_diff < 0.1:
                        self.logger.warning("原始换脸算法可能没有正常工作，返回了相同的图像")
                else:
                    self.logger.debug(f"原始换脸结果类型: img={type(img)}, result={type(swapped_result)}")
            else:
                # 如果没有原始方法，直接返回输入图像
                self.logger.warning("原始swap_core方法不可用")
                swapped_result = safe_tensor_to_numpy(img)
            
            # 2. 检查是否需要增强处理
            if not self._should_enhance(parameters, control):
                self._update_stats(time.time() - start_time, False, False)
                return swapped_result
            
            # 3. 应用增强处理
            if self.enhancer is not None:
                try:
                    # 准备增强处理的参数
                    # 注意：img是原始图像，swapped_result是换脸后的图像
                    target_img = safe_tensor_to_numpy(img)  # 原始目标图像
                    swapped_face = safe_tensor_to_numpy(swapped_result)  # 换脸后的图像

                    # 确保 kps_5 是 numpy 数组
                    if hasattr(kps_5, 'cpu'):
                        kps_5_np = kps_5.cpu().numpy()
                    elif isinstance(kps_5, list):
                        kps_5_np = np.array(kps_5)
                    else:
                        kps_5_np = kps_5

                    # 映射质量模式
                    quality_mode = parameters.get('QualityModeTextSel', 'Normal')
                    quality_mode_mapping = {
                        '快速': 'fast', 'Fast': 'fast',
                        '标准': 'normal', 'Normal': 'normal',
                        '高质量': 'high', 'High': 'high',
                        '极致': 'ultra', 'Ultra': 'ultra'
                    }
                    mapped_mode = quality_mode_mapping.get(quality_mode, 'normal')

                    # 调试日志
                    self.logger.info(f"质量模式切换到: {mapped_mode} (原始: {quality_mode})")
                    self.logger.info(f"增强功能启用: {self.config.enabled}")
                    self.logger.debug(f"参数形状: target_img={target_img.shape}, swapped_face={swapped_face.shape}")

                    # 验证参数
                    if target_img is None or swapped_face is None or kps_5_np is None:
                        self.logger.warning("增强处理参数不完整，跳过增强")
                        self._update_stats(time.time() - start_time, False, True)
                        return swapped_result

                    # 检查换脸是否成功（临时禁用严格检查）
                    img_diff = np.mean(np.abs(target_img.astype(float) - swapped_face.astype(float)))
                    self.logger.debug(f"换脸效果检测：图像差异={img_diff:.3f}")

                    if img_diff < 0.001:  # 极小的差异，可能是原始算法问题
                        self.logger.warning(f"原始换脸算法可能未工作（差异={img_diff:.3f}），强制应用增强")
                        # 不跳过，继续增强处理，可能可以改善效果
                    else:
                        self.logger.debug(f"换脸效果正常，差异={img_diff:.3f}，继续增强")

                    # 应用增强处理
                    enhanced_result = self.enhancer.process_face_swap(
                        target_img=target_img,
                        swapped_face=swapped_face,
                        face_landmarks=kps_5_np,
                        quality_mode=mapped_mode
                    )
                    
                    self._update_stats(time.time() - start_time, True, False)
                    return enhanced_result
                    
                except Exception as e:
                    self.logger.warning(f"增强处理失败，返回原始结果: {e}")
                    self.logger.debug(f"增强处理失败详情 - 参数类型: target_img={type(target_img)}, swapped_result={type(swapped_result)}, kps_5={type(kps_5)}")
                    self._update_stats(time.time() - start_time, False, True)

                    # 确保返回正确格式的结果
                    if isinstance(swapped_result, np.ndarray):
                        return swapped_result
                    else:
                        # 如果swapped_result不是numpy数组，尝试转换
                        try:
                            return np.array(swapped_result)
                        except:
                            # 最后的回退，返回原始输入
                            return img if isinstance(img, np.ndarray) else np.array(img)
            else:
                self._update_stats(time.time() - start_time, False, False)
                return swapped_result
                
        except Exception as e:
            self.logger.error(f"换脸包装器失败: {e}")
            self._update_stats(time.time() - start_time, False, True)

            # 最后的安全网：返回原始图像
            try:
                return safe_tensor_to_numpy(img)
            except:
                # 创建一个默认图像作为最后的回退
                return np.zeros((480, 640, 3), dtype=np.uint8)
    
    def _should_enhance(self, parameters: Dict[str, Any], control: Dict[str, Any]) -> bool:
        """判断是否应该进行增强处理"""
        # 强制增强模式 - 即使原始换脸无效也尝试增强
        force_enhance = parameters.get('ForceEnhancement', False)
        if force_enhance:
            self.logger.info("强制增强模式启用")
            return True
        """判断是否应该进行增强处理"""
        # 检查配置
        if not self.config.enabled:
            return False

        # 检查用户设置
        if not parameters.get('UseEnhancedSwapSwitch', True):
            return False

        # 检查质量模式 - 支持中英文
        quality_mode = parameters.get('QualityModeTextSel', 'Normal')
        quality_mode_mapping = {
            '快速': 'fast', 'Fast': 'fast',
            '标准': 'normal', 'Normal': 'normal',
            '高质量': 'high', 'High': 'high',
            '极致': 'ultra', 'Ultra': 'ultra'
        }

        mapped_mode = quality_mode_mapping.get(quality_mode, 'normal')

        if mapped_mode == 'fast':
            return False

        # 检查是否启用了换脸
        if not control.get('SwapFacesButton', False):
            return False

        return True
    
    def _update_stats(self, processing_time: float, enhanced: bool, fallback: bool):
        """更新统计信息"""
        self.stats['total_calls'] += 1
        
        if enhanced:
            self.stats['enhanced_calls'] += 1
        elif fallback:
            self.stats['fallback_calls'] += 1
        
        # 更新平均处理时间
        total = self.stats['total_calls']
        self.stats['avg_processing_time'] = (
            self.stats['avg_processing_time'] * (total - 1) + processing_time
        ) / total
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        if stats['total_calls'] > 0:
            stats['enhancement_rate'] = stats['enhanced_calls'] / stats['total_calls']
            stats['fallback_rate'] = stats['fallback_calls'] / stats['total_calls']
        else:
            stats['enhancement_rate'] = 0.0
            stats['fallback_rate'] = 0.0
        
        # 添加增强器统计
        if self.enhancer:
            enhancer_stats = self.enhancer.get_stats()
            stats.update({f'enhancer_{k}': v for k, v in enhancer_stats.items()})
        
        return stats
    
    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        try:
            # 更新配置
            for key, value in new_config.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
            
            # 重新初始化增强器
            if self.config.enabled and self.enhancer is None:
                self.enhancer = EnhancedFaceSwapperV2(self.config)
                self.logger.info("增强器已重新初始化")
            elif self.enhancer:
                self.enhancer.update_config(new_config)
            
            self.logger.info("安全换脸配置已更新")
            
        except Exception as e:
            self.logger.error(f"更新配置失败: {e}")

def integrate_safe_enhanced_swap(video_manager) -> Optional[SafeSwapIntegration]:
    """
    安全地集成增强换脸功能
    
    Args:
        video_manager: VideoManager实例
        
    Returns:
        SafeSwapIntegration实例或None
    """
    logger = get_logger("SafeSwapIntegration")
    
    try:
        # 创建安全集成器
        integration = SafeSwapIntegration(video_manager)
        
        # 检查VideoManager是否有swap_core方法
        if hasattr(video_manager, 'swap_core'):
            # 保存原始方法
            integration.original_swap_core = video_manager.swap_core
            
            # 替换为增强包装器
            video_manager.swap_core = integration.enhanced_swap_wrapper
            
            # 添加统计方法
            video_manager.get_enhanced_swap_stats = integration.get_stats
            video_manager.update_enhanced_swap_config = integration.update_config
            
            logger.info("安全增强换脸功能已成功集成")
            return integration
        else:
            logger.warning("VideoManager没有swap_core方法，无法集成")
            return None
            
    except Exception as e:
        logger.error(f"安全集成增强换脸功能失败: {e}")
        return None

def create_enhanced_config_v2():
    """创建增强配置V2"""
    return {
        "UseEnhancedSwap": True,
        "QualityMode": "normal",  # fast, normal, high, ultra
        "target_resolution": 512,
        "upscale_factor": 1.5,
        "enable_super_resolution": False,
        "blend_method": "alpha",  # alpha, poisson, seamless
        "blend_strength": 0.85,
        "feather_amount": 12,
        "enable_color_matching": True,
        "color_transfer_method": "histogram",
        "enable_face_enhancement": True,
        "enable_skin_smoothing": True,
        "skin_smooth_strength": 0.25,
        "use_half_precision": True,
        "enable_caching": True,
        "batch_processing": False
    }
