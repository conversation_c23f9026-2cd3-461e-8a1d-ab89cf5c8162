"""
统一日志系统
"""
import logging
import sys
from pathlib import Path
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from typing import Optional
import threading
from datetime import datetime

class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)

class ThreadSafeLogger:
    """线程安全的日志记录器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.loggers = {}
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
    
    def get_logger(self, name: str = "RopeLive", level: int = logging.INFO) -> logging.Logger:
        """获取或创建日志记录器"""
        if name in self.loggers:
            return self.loggers[name]
        
        logger = logging.getLogger(name)
        logger.setLevel(level)
        
        # 避免重复添加处理器
        if logger.handlers:
            self.loggers[name] = logger
            return logger
        
        # 文件处理器 - 按大小轮转
        file_handler = RotatingFileHandler(
            self.log_dir / f"{name.lower()}.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        
        # 错误文件处理器 - 只记录错误
        error_handler = RotatingFileHandler(
            self.log_dir / f"{name.lower()}_error.log",
            maxBytes=5*1024*1024,   # 5MB
            backupCount=3,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s'
        )
        
        simple_formatter = ColoredFormatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        
        file_handler.setFormatter(detailed_formatter)
        error_handler.setFormatter(detailed_formatter)
        console_handler.setFormatter(simple_formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(error_handler)
        logger.addHandler(console_handler)
        
        self.loggers[name] = logger
        return logger

class PerformanceLogger:
    """性能监控日志记录器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.start_times = {}
    
    def start_timer(self, operation: str):
        """开始计时"""
        self.start_times[operation] = datetime.now()
    
    def end_timer(self, operation: str, log_level: int = logging.INFO):
        """结束计时并记录"""
        if operation in self.start_times:
            duration = (datetime.now() - self.start_times[operation]).total_seconds()
            self.logger.log(log_level, f"操作 '{operation}' 耗时: {duration:.3f}秒")
            del self.start_times[operation]
        else:
            self.logger.warning(f"未找到操作 '{operation}' 的开始时间")
    
    def log_memory_usage(self):
        """记录内存使用情况"""
        try:
            # 尝试导入psutil，如果没有则跳过系统内存监控
            try:
                import psutil
                HAS_PSUTIL = True
            except ImportError:
                HAS_PSUTIL = False

            # 尝试导入torch，如果没有则跳过GPU内存监控
            try:
                import torch
                HAS_TORCH = True
            except ImportError:
                HAS_TORCH = False

            # 系统内存
            if HAS_PSUTIL:
                memory = psutil.virtual_memory()
                self.logger.info(f"系统内存使用: {memory.percent}% ({memory.used / 1024**3:.2f}GB / {memory.total / 1024**3:.2f}GB)")
            else:
                self.logger.debug("psutil 未安装，跳过系统内存监控")

            # GPU内存
            if HAS_TORCH and torch.cuda.is_available():
                for i in range(torch.cuda.device_count()):
                    allocated = torch.cuda.memory_allocated(i) / 1024**3
                    cached = torch.cuda.memory_reserved(i) / 1024**3
                    total = torch.cuda.get_device_properties(i).total_memory / 1024**3
                    self.logger.info(f"GPU {i} 内存: 已分配 {allocated:.2f}GB, 已缓存 {cached:.2f}GB, 总计 {total:.2f}GB")
            else:
                self.logger.debug("torch 未安装或CUDA不可用，跳过GPU内存监控")
        except Exception as e:
            self.logger.error(f"内存监控失败: {e}")

# 全局日志管理器
logger_manager = ThreadSafeLogger()

# 便捷函数
def get_logger(name: str = "RopeLive", level: int = logging.INFO) -> logging.Logger:
    """获取日志记录器的便捷函数"""
    return logger_manager.get_logger(name, level)

def get_performance_logger(name: str = "Performance") -> PerformanceLogger:
    """获取性能日志记录器"""
    logger = get_logger(name)
    return PerformanceLogger(logger)

# 装饰器用于自动记录函数执行时间
def log_execution_time(logger_name: str = "Performance"):
    """记录函数执行时间的装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            perf_logger = get_performance_logger(logger_name)
            operation_name = f"{func.__module__}.{func.__name__}"
            perf_logger.start_timer(operation_name)
            try:
                result = func(*args, **kwargs)
                perf_logger.end_timer(operation_name)
                return result
            except Exception as e:
                perf_logger.end_timer(operation_name, logging.ERROR)
                raise
        return wrapper
    return decorator

# 上下文管理器用于代码块计时
class LogExecutionTime:
    """代码块执行时间记录上下文管理器"""
    
    def __init__(self, operation_name: str, logger_name: str = "Performance"):
        self.operation_name = operation_name
        self.perf_logger = get_performance_logger(logger_name)
    
    def __enter__(self):
        self.perf_logger.start_timer(self.operation_name)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            self.perf_logger.end_timer(self.operation_name, logging.ERROR)
        else:
            self.perf_logger.end_timer(self.operation_name)

# 默认日志记录器
logger = get_logger()
