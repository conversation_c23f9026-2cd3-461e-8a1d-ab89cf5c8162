#!/usr/bin/env python3
"""
诊断原始换脸功能是否正常工作
"""

import sys
import os
import datetime

def setup_environment():
    """设置环境变量"""
    os.environ['ROPE_SESSION'] = 'diagnose_swap_' + str(int(datetime.datetime.now().timestamp()))
    os.environ['ROPE_LAUNCH_TIME'] = datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")

def check_swap_integration():
    """检查换脸集成状态"""
    try:
        print("检查换脸集成状态...")
        
        from rope.VideoManager import VideoManager
        import inspect
        
        # 检查VideoManager的swap_core方法
        if hasattr(VideoManager, 'swap_core'):
            print("✓ VideoManager有swap_core方法")
            
            # 检查方法是否被替换
            swap_core_source = inspect.getsource(VideoManager.swap_core)
            if "enhanced_swap_wrapper" in swap_core_source:
                print("✓ swap_core已被增强包装器替换")
            else:
                print("⚠️ swap_core可能是原始方法")
        else:
            print("❌ VideoManager没有swap_core方法")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查换脸集成失败: {e}")
        return False

def test_without_enhancement():
    """测试不启用增强功能的换脸"""
    try:
        print("测试不启用增强功能的换脸...")
        
        from rope.safe_swap_integration import SafeSwapIntegration
        import numpy as np
        
        # 创建模拟VideoManager
        class MockVideoManager:
            def __init__(self):
                self.swap_core = self.original_swap_core
                self.call_count = 0
            
            def original_swap_core(self, img, kps_5, kps, s_e, t_e, dfl_model, parameters, control):
                self.call_count += 1
                print(f"  原始swap_core被调用，第{self.call_count}次")
                
                # 模拟真实的换脸：修改图像
                if isinstance(img, np.ndarray):
                    result = img.copy()
                    # 在人脸区域做明显的修改
                    if len(result.shape) == 3 and result.shape[2] == 3:
                        # 增加红色通道，减少蓝色通道
                        result[:, :, 0] = np.clip(result[:, :, 0] + 30, 0, 255)  # 增加红色
                        result[:, :, 2] = np.clip(result[:, :, 2] - 30, 0, 255)  # 减少蓝色
                    print(f"  原始换脸处理完成，输入颜色: {img[50, 50] if img.shape[0] > 50 else 'N/A'}")
                    print(f"  原始换脸处理完成，输出颜色: {result[50, 50] if result.shape[0] > 50 else 'N/A'}")
                    return result
                else:
                    print(f"  输入不是numpy数组: {type(img)}")
                    return np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        vm = MockVideoManager()
        integration = SafeSwapIntegration(vm)
        
        # 设置集成
        integration.original_swap_core = vm.swap_core
        vm.swap_core = integration.enhanced_swap_wrapper
        
        # 测试数据
        test_img = np.full((100, 100, 3), [100, 150, 200], dtype=np.uint8)
        test_kps_5 = [[25, 25], [75, 25], [50, 50], [35, 75], [65, 75]]
        test_kps = np.random.rand(68, 2) * 100
        test_s_e = np.random.rand(512)
        test_t_e = np.random.rand(512)
        
        # 测试参数 - 禁用增强功能
        test_parameters = {
            'QualityModeTextSel': '快速',  # 快速模式禁用增强
            'UseEnhancedSwapSwitch': False  # 明确禁用增强
        }
        test_control = {'SwapFacesButton': True}
        
        print(f"输入图像颜色: {test_img[50, 50]}")
        
        # 调用换脸（应该只调用原始方法）
        result = vm.swap_core(test_img, test_kps_5, test_kps, test_s_e, test_t_e, 
                             False, test_parameters, test_control)
        
        print(f"最终结果颜色: {result[50, 50]}")
        print(f"原始方法调用次数: {vm.call_count}")
        
        # 验证结果
        if isinstance(result, np.ndarray):
            diff = np.mean(np.abs(result.astype(float) - test_img.astype(float)))
            print(f"图像变化程度: {diff:.2f}")
            
            if diff > 5.0:
                print("✓ 原始换脸功能正常工作")
                return True
            else:
                print("❌ 原始换脸功能可能有问题")
                return False
        else:
            print("❌ 返回结果类型错误")
            return False
        
    except Exception as e:
        print(f"❌ 测试原始换脸失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def create_bypass_enhancement():
    """创建绕过增强功能的临时修复"""
    try:
        print("创建绕过增强功能的临时修复...")
        
        # 临时修改_should_enhance方法，让它在原始换脸无效时仍然尝试增强
        integration_file = "rope/safe_swap_integration.py"
        
        with open(integration_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加强制增强模式
        if "# 强制增强模式" not in content:
            # 在_should_enhance方法开始添加强制模式
            pattern = "def _should_enhance(self, parameters: Dict[str, Any], control: Dict[str, Any]) -> bool:"
            replacement = '''def _should_enhance(self, parameters: Dict[str, Any], control: Dict[str, Any]) -> bool:
        """判断是否应该进行增强处理"""
        # 强制增强模式 - 即使原始换脸无效也尝试增强
        force_enhance = parameters.get('ForceEnhancement', False)
        if force_enhance:
            self.logger.info("强制增强模式启用")
            return True'''
            
            content = content.replace(pattern, replacement)
            
            with open(integration_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✓ 强制增强模式已添加")
        else:
            print("✓ 强制增强模式已存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建绕过增强失败: {e}")
        return False

def create_diagnostic_guide():
    """创建诊断指南"""
    try:
        print("创建诊断指南...")
        
        guide = """
# 原始换脸功能诊断指南

## 🔍 问题分析

### 当前现象
- 日志显示"换脸效果不明显（差异=0.000）"
- 这说明原始换脸算法可能没有正常工作
- 或者返回了与输入完全相同的图像

### 可能原因
1. **原始换脸算法问题**
   - 没有加载正确的模型
   - 人脸检测失败
   - 参数传递错误

2. **集成干扰**
   - 我们的增强集成可能干扰了原始流程
   - 参数传递有问题

3. **输入数据问题**
   - 源人脸图片没有正确加载
   - 目标图像格式不正确

## 🔧 诊断步骤

### 1. 测试基础换脸功能
1. **完全关闭增强功能**
   - 关闭"启用增强换脸"开关
   - 选择"快速"质量模式

2. **检查基础设置**
   - 确认源人脸图片已加载
   - 确认摄像头/视频正常工作
   - 确认人脸检测正常

3. **观察原始换脸效果**
   - 点击换脸按钮
   - 观察是否有任何变化
   - 查看控制台日志

### 2. 强制增强模式
如果原始换脸不工作，可以尝试强制增强：

在程序中添加参数：
```python
parameters['ForceEnhancement'] = True
```

### 3. 检查模型加载
确认以下模型正确加载：
- 人脸检测模型
- 换脸模型（如inswapper_128.fp16.onnx）
- 其他相关模型

## 🎯 预期结果

### 正常情况
```
原始换脸效果检查: 差异=45.67
换脸效果检测：图像差异=45.67，继续增强
```

### 异常情况
```
原始换脸效果检查: 差异=0.000
原始换脸算法可能没有正常工作，返回了相同的图像
```

## 💡 解决方案

### 如果原始换脸不工作
1. 检查模型文件是否存在
2. 重新加载源人脸图片
3. 确认人脸检测正常
4. 尝试不同的源图片

### 如果集成有问题
1. 临时禁用增强功能
2. 测试原始功能是否正常
3. 逐步启用增强功能

### 强制增强模式
即使原始换脸无效，也可以尝试增强处理，
可能会有意想不到的改善效果。

## 🚀 下一步

1. 运行诊断脚本
2. 检查原始换脸功能
3. 根据结果调整策略
4. 必要时使用强制增强模式
"""
        
        with open("原始换脸诊断指南.md", 'w', encoding='utf-8') as f:
            f.write(guide)
        
        print("✓ 诊断指南已创建")
        return True
        
    except Exception as e:
        print(f"❌ 创建诊断指南失败: {e}")
        return False

def show_diagnostic_summary():
    """显示诊断总结"""
    print("\n" + "="*60)
    print("🔍 原始换脸功能诊断总结")
    print("="*60)
    
    print("\n📋 发现的问题:")
    print("  ⚠️ 原始换脸算法可能没有正常工作")
    print("  ⚠️ 返回图像与输入图像完全相同")
    print("  ⚠️ 差异值为0.000表示无任何变化")
    
    print("\n🔧 诊断方法:")
    print("  • 测试不启用增强的换脸")
    print("  • 检查原始方法调用")
    print("  • 验证图像变化程度")
    print("  • 添加强制增强模式")
    
    print("\n🎯 下一步:")
    print("  1. 检查基础换脸功能")
    print("  2. 确认模型正确加载")
    print("  3. 验证源人脸图片")
    print("  4. 必要时使用强制增强")
    
    print("\n💡 临时解决方案:")
    print("  • 已禁用严格的差异检查")
    print("  • 增强功能会强制运行")
    print("  • 可能仍能改善效果")

def main():
    """主函数"""
    print("=" * 60)
    print("Rope Live Stellar - 原始换脸功能诊断")
    print("=" * 60)
    
    # 设置环境
    setup_environment()
    
    diagnostics = [
        ("检查换脸集成状态", check_swap_integration),
        ("测试不启用增强的换脸", test_without_enhancement),
        ("创建绕过增强功能", create_bypass_enhancement),
        ("创建诊断指南", create_diagnostic_guide),
    ]
    
    completed = 0
    total = len(diagnostics)
    
    for diag_name, diag_func in diagnostics:
        print(f"\n--- {diag_name} ---")
        try:
            if diag_func():
                completed += 1
                print(f"✅ {diag_name} 完成")
            else:
                print(f"⚠️ {diag_name} 发现问题")
        except Exception as e:
            print(f"❌ {diag_name} 失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"诊断结果: {completed}/{total} 完成")
    
    show_diagnostic_summary()
    
    return completed >= 2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
