#!/usr/bin/env python3
"""
测试质量模式GUI集成
"""

import sys
import os
import datetime

def setup_environment():
    """设置环境变量"""
    os.environ['ROPE_SESSION'] = 'quality_test_' + str(int(datetime.datetime.now().timestamp()))
    os.environ['ROPE_LAUNCH_TIME'] = datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")

def test_dict_definitions():
    """测试字典定义"""
    try:
        print("测试字典定义...")
        
        # 测试中文字典
        from rope.Dicts_CN import get_current_dict as get_cn_dict
        cn_dict = get_cn_dict()
        
        required_cn_keys = [
            'QualityModeTitle',
            'UseEnhancedSwapTitle', 
            'QualityModeTextSelMode',
            'QualityModeTextSelModes',
            'UseEnhancedSwapSwitchState'
        ]
        
        for key in required_cn_keys:
            if key in cn_dict:
                print(f"✓ 中文字典 {key}: {cn_dict[key]}")
            else:
                print(f"❌ 中文字典缺少 {key}")
                return False
        
        # 测试英文字典
        from rope.Dicts_EN import get_current_dict as get_en_dict
        en_dict = get_en_dict()
        
        required_en_keys = [
            'QualityModeTitle',
            'UseEnhancedSwapTitle',
            'QualityModeTextSelMode', 
            'QualityModeTextSelModes',
            'UseEnhancedSwapSwitchState'
        ]
        
        for key in required_en_keys:
            if key in en_dict:
                print(f"✓ 英文字典 {key}: {en_dict[key]}")
            else:
                print(f"❌ 英文字典缺少 {key}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 字典定义测试失败: {e}")
        return False

def test_gui_elements():
    """测试GUI元素"""
    try:
        print("测试GUI元素...")
        
        # 测试GUIElements导入
        from rope.GUIElements import TextSelection, Switch2
        print("✓ GUIElements导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI元素测试失败: {e}")
        return False

def test_safe_integration():
    """测试安全集成"""
    try:
        print("测试安全集成...")
        
        from rope.safe_swap_integration import SafeSwapIntegration
        print("✓ SafeSwapIntegration导入成功")
        
        # 测试质量模式映射
        class MockVideoManager:
            pass
        
        vm = MockVideoManager()
        integration = SafeSwapIntegration(vm)
        
        # 测试中文质量模式
        test_params_cn = {
            'QualityModeTextSel': '高质量',
            'UseEnhancedSwapSwitch': True
        }
        test_control = {'SwapFacesButton': True}
        
        should_enhance = integration._should_enhance(test_params_cn, test_control)
        print(f"✓ 中文高质量模式测试: {should_enhance}")
        
        # 测试英文质量模式
        test_params_en = {
            'QualityModeTextSel': 'Fast',
            'UseEnhancedSwapSwitch': True
        }
        
        should_enhance_fast = integration._should_enhance(test_params_en, test_control)
        print(f"✓ 英文快速模式测试: {should_enhance_fast} (应该为False)")
        
        return True
        
    except Exception as e:
        print(f"❌ 安全集成测试失败: {e}")
        return False

def create_gui_test_instructions():
    """创建GUI测试说明"""
    try:
        print("创建GUI测试说明...")
        
        instructions = """
# 质量模式GUI测试说明

## 🎯 测试步骤

### 1. 启动程序
```bash
.\venv3.10\python.exe Rope.py
```

### 2. 查找质量模式选项
在程序界面中，找到以下新增的控件：

#### 在"设置"面板中：
- **质量模式** (Quality Mode) 下拉选择框
  - 选项：快速 / 标准 / 高质量 / 极致
  - 默认：标准

- **启用增强换脸** (Enhanced Swap) 开关
  - 默认：开启

### 3. 测试功能
1. **选择不同质量模式**：
   - 快速：应该禁用增强功能
   - 标准：启用基础增强
   - 高质量：启用完整增强
   - 极致：启用所有增强功能

2. **测试增强开关**：
   - 关闭时：使用原始算法
   - 开启时：使用增强算法

### 4. 验证效果
1. 加载源人脸图片
2. 选择摄像头或视频
3. 启用换脸功能
4. 切换不同质量模式
5. 观察效果差异

## 📊 预期效果

### 质量模式对比：
- **快速**: 最快速度，原始质量
- **标准**: 轻微增强，平衡性能
- **高质量**: 明显改善，稍慢
- **极致**: 最佳效果，最慢

### 控制台输出：
应该看到类似信息：
```
✓ 安全增强换脸功能已集成
增强换脸处理器V2初始化完成 - 质量模式: normal
```

## 🔧 故障排除

### 如果看不到质量模式选项：
1. 检查是否重启了程序
2. 查看控制台是否有错误
3. 确认字典文件已更新

### 如果增强功能不工作：
1. 检查"启用增强换脸"开关
2. 确认质量模式不是"快速"
3. 查看控制台错误信息

## 📍 界面位置

质量模式选项应该出现在：
**设置面板 → 换脸参数 → 换脸模型下方**

位置大约在：
- 换脸模型 (FSM)
- 换脸像素 (SwapRes)  
- **质量模式** ← 新增
- **启用增强换脸** ← 新增
- 摄像头后端 (CamBack)
"""
        
        with open("质量模式GUI测试说明.md", 'w', encoding='utf-8') as f:
            f.write(instructions)
        
        print("✓ GUI测试说明已创建")
        return True
        
    except Exception as e:
        print(f"❌ 创建GUI测试说明失败: {e}")
        return False

def show_test_summary():
    """显示测试总结"""
    print("\n" + "="*60)
    print("🎯 质量模式GUI集成测试总结")
    print("="*60)
    
    print("\n📋 已添加的GUI控件:")
    print("  ✓ 质量模式下拉选择框")
    print("    - 快速 / 标准 / 高质量 / 极致")
    print("  ✓ 启用增强换脸开关")
    print("    - 控制是否使用增强算法")
    
    print("\n🌐 多语言支持:")
    print("  ✓ 中文界面: 快速/标准/高质量/极致")
    print("  ✓ 英文界面: Fast/Normal/High/Ultra")
    
    print("\n⚙️ 功能集成:")
    print("  ✓ 质量模式映射到增强算法")
    print("  ✓ 安全集成避免递归调用")
    print("  ✓ 实时配置更新")
    
    print("\n📍 界面位置:")
    print("  设置面板 → 换脸参数 → 换脸模型下方")
    
    print("\n🚀 下一步:")
    print("  1. 重启程序")
    print("  2. 查找新增的质量模式选项")
    print("  3. 测试不同质量模式的效果")
    print("  4. 参考 '质量模式GUI测试说明.md'")

def main():
    """主函数"""
    print("=" * 60)
    print("Rope Live Stellar - 质量模式GUI集成测试")
    print("=" * 60)
    
    # 设置环境
    setup_environment()
    
    tests = [
        ("测试字典定义", test_dict_definitions),
        ("测试GUI元素", test_gui_elements),
        ("测试安全集成", test_safe_integration),
        ("创建GUI测试说明", create_gui_test_instructions),
    ]
    
    completed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                completed += 1
                print(f"✅ {test_name} 完成")
            else:
                print(f"⚠️ {test_name} 部分完成")
        except Exception as e:
            print(f"❌ {test_name} 失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {completed}/{total} 完成")
    
    if completed >= 3:  # 至少完成主要测试
        print("🎉 质量模式GUI集成测试成功！")
        show_test_summary()
        
        return True
    else:
        print("⚠️ 部分测试失败，请检查错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
