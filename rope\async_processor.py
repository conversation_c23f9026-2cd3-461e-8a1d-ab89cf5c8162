"""
异步处理系统
"""
import asyncio
import threading
from concurrent.futures import ThreadPoolExecutor, Future
from typing import AsyncGenerator, Callable, Any, Optional, Dict, List
from queue import Queue, Empty
import time
import numpy as np
from dataclasses import dataclass

from .logger import get_logger, LogExecutionTime
from .error_handler import handle_error, ErrorSeverity, ErrorCategory
from .resource_manager import resource_manager

@dataclass
class ProcessingTask:
    """处理任务"""
    task_id: str
    data: Any
    callback: Optional[Callable] = None
    priority: int = 0
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()

class AsyncVideoProcessor:
    """异步视频处理器"""
    
    def __init__(self, max_workers: int = 4, queue_size: int = 10):
        self.max_workers = max_workers
        self.queue_size = queue_size
        self.logger = get_logger("AsyncProcessor")
        
        # 任务队列
        self.task_queue = asyncio.Queue(maxsize=queue_size)
        self.result_queue = asyncio.Queue()
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 状态管理
        self.is_running = False
        self.workers: List[asyncio.Task] = []
        self.processed_count = 0
        self.error_count = 0
        
        # 性能统计
        self.processing_times: List[float] = []
        self.max_stats_size = 100
    
    async def start(self):
        """启动异步处理器"""
        if self.is_running:
            return
        
        self.is_running = True
        self.logger.info(f"启动异步处理器，工作线程数: {self.max_workers}")
        
        # 启动工作协程
        for i in range(self.max_workers):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.workers.append(worker)
    
    async def stop(self):
        """停止异步处理器"""
        if not self.is_running:
            return
        
        self.is_running = False
        self.logger.info("停止异步处理器...")
        
        # 等待所有任务完成
        for worker in self.workers:
            worker.cancel()
        
        await asyncio.gather(*self.workers, return_exceptions=True)
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        self.logger.info("异步处理器已停止")
    
    async def submit_task(self, task: ProcessingTask) -> bool:
        """提交处理任务"""
        try:
            await self.task_queue.put(task)
            return True
        except asyncio.QueueFull:
            self.logger.warning(f"任务队列已满，丢弃任务: {task.task_id}")
            return False
    
    async def get_result(self, timeout: float = 1.0) -> Optional[Any]:
        """获取处理结果"""
        try:
            return await asyncio.wait_for(self.result_queue.get(), timeout=timeout)
        except asyncio.TimeoutError:
            return None
    
    async def _worker(self, worker_name: str):
        """工作协程"""
        self.logger.debug(f"工作协程 {worker_name} 启动")
        
        while self.is_running:
            try:
                # 获取任务
                task = await asyncio.wait_for(self.task_queue.get(), timeout=1.0)
                
                # 处理任务
                await self._process_task(task, worker_name)
                
            except asyncio.TimeoutError:
                continue
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.error_count += 1
                handle_error(e, ErrorSeverity.MEDIUM, ErrorCategory.SYSTEM, 
                           f"工作协程 {worker_name} 处理任务失败")
        
        self.logger.debug(f"工作协程 {worker_name} 停止")
    
    async def _process_task(self, task: ProcessingTask, worker_name: str):
        """处理单个任务"""
        start_time = time.time()
        
        try:
            with LogExecutionTime(f"async_task_{task.task_id}"):
                # 在线程池中执行CPU密集型任务
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    self.executor, 
                    self._execute_task, 
                    task
                )
                
                # 将结果放入结果队列
                await self.result_queue.put({
                    'task_id': task.task_id,
                    'result': result,
                    'worker': worker_name,
                    'processing_time': time.time() - start_time
                })
                
                # 执行回调
                if task.callback:
                    try:
                        task.callback(result)
                    except Exception as callback_error:
                        self.logger.error(f"任务回调执行失败: {callback_error}")
                
                # 更新统计
                self.processed_count += 1
                processing_time = time.time() - start_time
                self.processing_times.append(processing_time)
                
                # 限制统计数据大小
                if len(self.processing_times) > self.max_stats_size:
                    self.processing_times.pop(0)
                
        except Exception as e:
            self.error_count += 1
            handle_error(e, ErrorSeverity.MEDIUM, ErrorCategory.SYSTEM, 
                        f"任务处理失败: {task.task_id}")
    
    def _execute_task(self, task: ProcessingTask) -> Any:
        """在线程池中执行任务"""
        # 这里可以根据任务类型执行不同的处理逻辑
        # 例如：图像处理、模型推理等
        return task.data
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        if not self.processing_times:
            avg_time = 0
            max_time = 0
            min_time = 0
        else:
            avg_time = sum(self.processing_times) / len(self.processing_times)
            max_time = max(self.processing_times)
            min_time = min(self.processing_times)
        
        return {
            'processed_count': self.processed_count,
            'error_count': self.error_count,
            'queue_size': self.task_queue.qsize(),
            'result_queue_size': self.result_queue.qsize(),
            'avg_processing_time': avg_time,
            'max_processing_time': max_time,
            'min_processing_time': min_time,
            'is_running': self.is_running,
            'worker_count': len(self.workers)
        }

class FrameProcessor:
    """帧处理器，专门用于视频帧处理"""
    
    def __init__(self, processor_func: Callable, max_workers: int = 2):
        self.processor_func = processor_func
        self.async_processor = AsyncVideoProcessor(max_workers=max_workers)
        self.logger = get_logger("FrameProcessor")
        
        # 注册自定义任务执行器
        self.async_processor._execute_task = self._process_frame
    
    async def start(self):
        """启动帧处理器"""
        await self.async_processor.start()
    
    async def stop(self):
        """停止帧处理器"""
        await self.async_processor.stop()
    
    async def process_frame(self, frame: np.ndarray, frame_id: str, 
                          callback: Optional[Callable] = None) -> bool:
        """处理视频帧"""
        task = ProcessingTask(
            task_id=frame_id,
            data=frame,
            callback=callback,
            priority=1
        )
        
        return await self.async_processor.submit_task(task)
    
    def _process_frame(self, task: ProcessingTask) -> np.ndarray:
        """处理单帧"""
        try:
            with resource_manager.gpu_memory_context():
                return self.processor_func(task.data)
        except Exception as e:
            self.logger.error(f"帧处理失败: {e}")
            return task.data  # 返回原始帧
    
    async def get_processed_frame(self, timeout: float = 1.0) -> Optional[Dict]:
        """获取处理后的帧"""
        return await self.async_processor.get_result(timeout)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取处理统计"""
        return self.async_processor.get_statistics()

# 全局异步处理器实例
global_async_processor = AsyncVideoProcessor()

# 便捷函数
async def start_async_processing():
    """启动全局异步处理"""
    await global_async_processor.start()

async def stop_async_processing():
    """停止全局异步处理"""
    await global_async_processor.stop()

def create_frame_processor(processor_func: Callable, max_workers: int = 2) -> FrameProcessor:
    """创建帧处理器"""
    return FrameProcessor(processor_func, max_workers)
