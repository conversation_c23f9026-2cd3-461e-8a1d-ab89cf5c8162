# #!/usr/bin/env python3

import time
import torch
import traceback
import sys
import os

import rope.GUI as GUI
import rope.VideoManager as VM
import rope.Models as Models
from rope.Dicts import DEFAULT_DATA

resize_delay = 1
mem_delay = 1

def quit_app():
    vm.terminate_audio_process_tree()
    gui.destroy()

# @profile
def coordinator():
    global gui, vm, action, frame, r_frame, load_notice, resize_delay, mem_delay

    if gui.get_action_length() > 0:
        action.append(gui.get_action())

    if vm.get_action_length() > 0:
        action.append(vm.get_action())

    if vm.get_frame_length() > 0:
        frame.append(vm.get_frame())

    if len(frame) > 0:
        gui.set_image(frame[0], False)
        frame.pop(0)

    if vm.get_requested_frame_length() > 0:
        r_frame.append(vm.get_requested_frame())

    if len(r_frame) > 0:
        gui.set_image(r_frame[0], True)
        r_frame=[]

    if len(action) > 0:
        # print('Action:', action[0][0])
        # print('Value:', action[0][1])
        if action[0][0] == "load_target_video":
            vm.load_target_video(action[0][1])
            action.pop(0)

        elif action[0][0] == "load_target_image":
            vm.load_target_image(action[0][1])
            action.pop(0)

        elif action[0][0] == "play_video":
            vm.play_video(action[0][1])
            action.pop(0)

        elif action[0][0] == "get_requested_video_frame":
            vm.get_requested_video_frame(action[0][1], marker=True)
            action.pop(0)

        elif action[0][0] == "get_requested_video_frame_without_markers":
            vm.get_requested_video_frame(action[0][1], marker=False)
            action.pop(0)

        elif action[0][0] == "enable_virtualcam":
            vm.enable_virtualcam()
            action.pop(0)

        elif action[0][0] == "disable_virtualcam":
            vm.disable_virtualcam()
            action.pop(0)

        elif action[0][0] == "change_webcam_resolution_and_fps":
            vm.change_webcam_resolution_and_fps()
            action.pop(0)

        elif action[0][0] == "target_faces":
            vm.assign_found_faces(action[0][1])
            action.pop(0)

        elif action [0][0] == "saved_video_path":
            vm.saved_video_path = action[0][1]
            action.pop(0)

        elif action [0][0] == "vid_qual":
            vm.vid_qual = int(action[0][1])
            action.pop(0)

        elif action [0][0] == "set_stop":
            vm.stop_marker = action[0][1]
            action.pop(0)

        elif action [0][0] == "perf_test":
            vm.perf_test = action[0][1]
            action.pop(0)

        elif action [0][0] == 'ui_vars':
            vm.ui_data = action[0][1]
            action.pop(0)

        elif action [0][0] == 'control':
            vm.control = action[0][1]
            action.pop(0)

        elif action [0][0] == "parameters":
            vm.parameters = action[0][1]
            action.pop(0)

        # Face Editor
        elif action [0][0] == "parameters_face_editor":
            vm.parameters_face_editor = action[0][1]
            action.pop(0)

        elif action [0][0] == "markers":
            vm.markers = action[0][1]
            action.pop(0)

        elif action[0][0] == "function":
            eval(action[0][1])
            action.pop(0)

        elif action [0][0] == "clear_mem":
            vm.clear_mem()
            action.pop(0)

        # From VM
        elif action[0][0] == "stop_play":
            gui.set_player_buttons_to_inactive()
            action.pop(0)

        elif action[0][0] == "set_virtual_cam_toggle_disable":
            gui.set_virtual_cam_toggle_disable()
            action.pop(0)

        elif action[0][0] == "disable_record_button":
            gui.disable_record_button()
            action.pop(0)

        elif action[0][0] == "clear_faces_stop_swap":
            gui.clear_faces()
            gui.toggle_swapper(0)
            action.pop(0)

        elif action[0][0] == "clear_stop_enhance":
            gui.toggle_enhancer(0)
            action.pop(0)

        elif action[0][0] == "clear_stop_faces_editor":
            gui.toggle_faces_editor(0)
            action.pop(0)

        elif action[0][0] == "set_slider_length":
            gui.set_video_slider_length(action[0][1])
            action.pop(0)

        elif action[0][0] == "set_slider_fps":
            gui.set_video_slider_fps(action[0][1])
            action.pop(0)

        elif action[0][0] == "update_markers_canvas":
            gui.update_markers_canvas()
            action.pop(0)

        # Face Landmarks
        elif action[0][0] == "face_landmarks":
            vm.face_landmarks = action[0][1]
            action.pop(0)

        # Face Editor
        elif action[0][0] == "face_editor":
            vm.face_editor = action[0][1]
            action.pop(0)

        else:
            print("Action not found: "+action[0][0]+" "+str(action[0][1]))
            action.pop(0)

    if resize_delay > 100:
        gui.check_for_video_resize()
        resize_delay = 0
    else:
        resize_delay +=1

    if mem_delay > 1000:
        gui.update_vram_indicator()
        mem_delay = 0
    else:
        mem_delay +=1

    vm.process()
    gui.after(1, coordinator)
    # print(time.time() - start)

def run():
    """优化的协调器运行函数"""
    from rope.logger import get_logger, get_performance_logger
    from rope.error_handler import handle_error, ErrorSeverity, ErrorCategory
    from rope.resource_manager import resource_manager
    from rope.config import config_manager, SOFTWARE_VERSION
    from rope.event_system import publish, EventType

    logger = get_logger("Coordinator")
    perf_logger = get_performance_logger()

    try:
        global gui, vm, action, frame, r_frame, resize_delay, mem_delay

        logger.info("初始化协调器...")
        perf_logger.start_timer("coordinator_initialization")

        # 发布系统启动事件
        publish(EventType.SYSTEM_STARTUP, {"component": "coordinator"})

        # 检测设备
        device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"使用设备: {device}")

        # 配置提供者优先级
        if device == "cuda":
            DEFAULT_DATA['ProvidersPriorityTextSelMode'] = 'CUDA'
            DEFAULT_DATA['ProvidersPriorityTextSelModes'] = ['CUDA', 'TensorRT', 'TensorRT-Engine', 'CPU']
        else:
            DEFAULT_DATA['ProvidersPriorityTextSelMode'] = 'CPU'
            DEFAULT_DATA['ProvidersPriorityTextSelModes'] = ['CPU']

        # 初始化核心组件
        logger.info("初始化模型管理器...")
        models = Models.Models(device=device)

        logger.info("初始化GUI...")
        gui = GUI.GUI(models, software_version=SOFTWARE_VERSION)

        logger.info("初始化视频管理器...")
        vm = VM.VideoManager(models)

        # 建立组件间的引用关系
        vm.gui = gui
        models.vm = vm

        # 初始化消息队列
        action = []
        frame = []
        r_frame = []

        # 注册资源清理
        resource_manager.register_cleanup(lambda: quit_app(), priority=50)

        # 初始化GUI
        logger.info("初始化用户界面...")
        gui.initialize_gui()
        gui.protocol("WM_DELETE_WINDOW", lambda: quit_app())

        # 发布UI初始化完成事件
        publish(EventType.UI_INITIALIZED, {"software_version": SOFTWARE_VERSION})

        perf_logger.end_timer("coordinator_initialization")
        logger.info("协调器初始化完成")

        # 启动主循环
        logger.info("启动协调器主循环...")
        coordinator()
        gui.mainloop()

    except Exception as e:
        error_msg = f"协调器运行失败: {str(e)}"
        logger.critical(error_msg, exc_info=True)

        # 发布系统错误事件
        publish(EventType.SYSTEM_ERROR, {
            "component": "coordinator",
            "error": str(e),
            "traceback": traceback.format_exc()
        })

        # 使用统一错误处理
        if not handle_error(e, ErrorSeverity.CRITICAL, ErrorCategory.SYSTEM, "协调器运行失败"):
            sys.exit(1)

    finally:
        # 发布系统关闭事件
        publish(EventType.SYSTEM_SHUTDOWN, {"component": "coordinator"})
