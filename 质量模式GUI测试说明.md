
# 质量模式GUI测试说明

## 🎯 测试步骤

### 1. 启动程序
```bash
.env3.10\python.exe Rope.py
```

### 2. 查找质量模式选项
在程序界面中，找到以下新增的控件：

#### 在"设置"面板中：
- **质量模式** (Quality Mode) 下拉选择框
  - 选项：快速 / 标准 / 高质量 / 极致
  - 默认：标准

- **启用增强换脸** (Enhanced Swap) 开关
  - 默认：开启

### 3. 测试功能
1. **选择不同质量模式**：
   - 快速：应该禁用增强功能
   - 标准：启用基础增强
   - 高质量：启用完整增强
   - 极致：启用所有增强功能

2. **测试增强开关**：
   - 关闭时：使用原始算法
   - 开启时：使用增强算法

### 4. 验证效果
1. 加载源人脸图片
2. 选择摄像头或视频
3. 启用换脸功能
4. 切换不同质量模式
5. 观察效果差异

## 📊 预期效果

### 质量模式对比：
- **快速**: 最快速度，原始质量
- **标准**: 轻微增强，平衡性能
- **高质量**: 明显改善，稍慢
- **极致**: 最佳效果，最慢

### 控制台输出：
应该看到类似信息：
```
✓ 安全增强换脸功能已集成
增强换脸处理器V2初始化完成 - 质量模式: normal
```

## 🔧 故障排除

### 如果看不到质量模式选项：
1. 检查是否重启了程序
2. 查看控制台是否有错误
3. 确认字典文件已更新

### 如果增强功能不工作：
1. 检查"启用增强换脸"开关
2. 确认质量模式不是"快速"
3. 查看控制台错误信息

## 📍 界面位置

质量模式选项应该出现在：
**设置面板 → 换脸参数 → 换脸模型下方**

位置大约在：
- 换脸模型 (FSM)
- 换脸像素 (SwapRes)  
- **质量模式** ← 新增
- **启用增强换脸** ← 新增
- 摄像头后端 (CamBack)
