"""
增强换脸功能 V2.0
重新设计的架构，避免递归调用问题
"""

import torch
import torch.nn.functional as F
import cv2
import numpy as np
from typing import Tuple, Optional, Dict, Any, Union
import time
from dataclasses import dataclass
from pathlib import Path

from .logger import get_logger
from .config import config_manager

@dataclass
class EnhancedSwapConfigV2:
    """增强换脸配置 V2"""
    # 基础设置
    enabled: bool = True
    quality_mode: str = "normal"  # fast, normal, high, ultra
    
    # 图像处理
    target_resolution: int = 512
    upscale_factor: float = 1.5
    enable_super_resolution: bool = False
    
    # 混合算法
    blend_method: str = "alpha"  # alpha, poisson, seamless
    blend_strength: float = 0.85
    feather_amount: int = 12
    
    # 色彩处理
    enable_color_matching: bool = True
    color_transfer_method: str = "histogram"
    
    # 后处理
    enable_face_enhancement: bool = True
    enable_skin_smoothing: bool = True
    skin_smooth_strength: float = 0.25
    
    # 性能设置
    use_half_precision: bool = True
    enable_caching: bool = True
    batch_processing: bool = False

class FaceEnhancementProcessor:
    """人脸增强处理器"""
    
    def __init__(self, config: EnhancedSwapConfigV2):
        self.config = config
        self.logger = get_logger("FaceEnhancement")
        
        # 缓存常用的滤波器
        self._gaussian_kernels = {}
        self._bilateral_cache = {}
        
        self._init_processors()
    
    def _init_processors(self):
        """初始化处理器"""
        # 预计算常用的高斯核
        for sigma in [0.5, 1.0, 1.5, 2.0]:
            kernel_size = int(6 * sigma + 1)
            if kernel_size % 2 == 0:
                kernel_size += 1
            
            kernel = cv2.getGaussianKernel(kernel_size, sigma)
            kernel = torch.from_numpy(kernel @ kernel.T).float()
            self._gaussian_kernels[sigma] = kernel.unsqueeze(0).unsqueeze(0)
    
    def enhance_face_alignment(self, face_img: np.ndarray, landmarks: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """增强人脸对齐"""
        try:
            # 标准人脸关键点位置
            target_landmarks = np.array([
                [38.2946, 51.6963],  # 左眼
                [73.5318, 51.5014],  # 右眼
                [56.0252, 71.7366],  # 鼻子
                [41.5493, 92.3655],  # 左嘴角
                [70.7299, 92.2041]   # 右嘴角
            ]) * (self.config.target_resolution / 112.0)
            
            # 计算仿射变换矩阵
            transform_matrix = cv2.estimateAffinePartial2D(
                landmarks.astype(np.float32),
                target_landmarks.astype(np.float32),
                method=cv2.LMEDS
            )[0]
            
            # 应用变换
            aligned_face = cv2.warpAffine(
                face_img,
                transform_matrix,
                (self.config.target_resolution, self.config.target_resolution),
                flags=cv2.INTER_LANCZOS4,
                borderMode=cv2.BORDER_REFLECT
            )
            
            return aligned_face, transform_matrix
            
        except Exception as e:
            self.logger.warning(f"人脸对齐失败，使用原图: {e}")
            return face_img, np.eye(2, 3, dtype=np.float32)
    
    def enhance_image_quality(self, img: np.ndarray) -> np.ndarray:
        """增强图像质量"""
        if not self.config.enable_face_enhancement:
            return img
        
        try:
            # 1. 自适应直方图均衡化
            if len(img.shape) == 3:
                lab = cv2.cvtColor(img, cv2.COLOR_RGB2LAB)
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                lab[:, :, 0] = clahe.apply(lab[:, :, 0])
                enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)
            else:
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                enhanced = clahe.apply(img)
            
            # 2. 轻微锐化
            kernel = np.array([[-0.1, -0.1, -0.1],
                              [-0.1,  1.8, -0.1],
                              [-0.1, -0.1, -0.1]])
            sharpened = cv2.filter2D(enhanced, -1, kernel)
            
            # 3. 混合原图和增强结果
            result = cv2.addWeighted(enhanced, 0.7, sharpened, 0.3, 0)
            
            return np.clip(result, 0, 255).astype(np.uint8)
            
        except Exception as e:
            self.logger.warning(f"图像质量增强失败: {e}")
            return img
    
    def apply_skin_smoothing(self, img: np.ndarray) -> np.ndarray:
        """应用皮肤平滑"""
        if not self.config.enable_skin_smoothing:
            return img
        
        try:
            # 双边滤波进行皮肤平滑
            smoothed = cv2.bilateralFilter(
                img, 
                d=9, 
                sigmaColor=75, 
                sigmaSpace=75
            )
            
            # 创建简单的皮肤掩码
            skin_mask = self._create_skin_mask(img)
            
            # 应用平滑
            strength = self.config.skin_smooth_strength
            result = img.astype(np.float32) * (1 - skin_mask * strength) + \
                    smoothed.astype(np.float32) * (skin_mask * strength)
            
            return np.clip(result, 0, 255).astype(np.uint8)
            
        except Exception as e:
            self.logger.warning(f"皮肤平滑失败: {e}")
            return img
    
    def _create_skin_mask(self, img: np.ndarray) -> np.ndarray:
        """创建皮肤掩码"""
        try:
            # 转换到HSV色彩空间
            hsv = cv2.cvtColor(img, cv2.COLOR_RGB2HSV)
            
            # 皮肤色彩范围
            lower_skin = np.array([0, 20, 70], dtype=np.uint8)
            upper_skin = np.array([20, 255, 255], dtype=np.uint8)
            
            mask = cv2.inRange(hsv, lower_skin, upper_skin)
            
            # 形态学操作
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
            
            # 高斯模糊软化边缘
            mask = cv2.GaussianBlur(mask, (11, 11), 0)
            
            return mask.astype(np.float32) / 255.0
            
        except Exception as e:
            self.logger.warning(f"创建皮肤掩码失败: {e}")
            return np.zeros((img.shape[0], img.shape[1]), dtype=np.float32)

class ColorMatchingProcessor:
    """色彩匹配处理器"""
    
    def __init__(self, config: EnhancedSwapConfigV2):
        self.config = config
        self.logger = get_logger("ColorMatching")
    
    def match_colors(self, source: np.ndarray, target: np.ndarray) -> np.ndarray:
        """色彩匹配"""
        if not self.config.enable_color_matching:
            return source
        
        try:
            if self.config.color_transfer_method == "histogram":
                return self._histogram_matching(source, target)
            elif self.config.color_transfer_method == "lab":
                return self._lab_color_transfer(source, target)
            else:
                return self._simple_color_transfer(source, target)
                
        except Exception as e:
            self.logger.warning(f"色彩匹配失败: {e}")
            return source
    
    def _histogram_matching(self, source: np.ndarray, target: np.ndarray) -> np.ndarray:
        """直方图匹配"""
        matched = np.zeros_like(source)
        
        for i in range(3):  # RGB三个通道
            source_hist, _ = np.histogram(source[:, :, i], bins=256, range=(0, 256))
            target_hist, _ = np.histogram(target[:, :, i], bins=256, range=(0, 256))
            
            # 计算累积分布函数
            source_cdf = source_hist.cumsum()
            target_cdf = target_hist.cumsum()
            
            # 归一化
            source_cdf = source_cdf / (source_cdf[-1] + 1e-8)
            target_cdf = target_cdf / (target_cdf[-1] + 1e-8)
            
            # 创建映射表
            mapping = np.zeros(256, dtype=np.uint8)
            for j in range(256):
                idx = np.argmin(np.abs(target_cdf - source_cdf[j]))
                mapping[j] = idx
            
            matched[:, :, i] = mapping[source[:, :, i]]
        
        return matched
    
    def _lab_color_transfer(self, source: np.ndarray, target: np.ndarray) -> np.ndarray:
        """LAB色彩空间色彩迁移"""
        # 转换到LAB空间
        source_lab = cv2.cvtColor(source, cv2.COLOR_RGB2LAB).astype(np.float32)
        target_lab = cv2.cvtColor(target, cv2.COLOR_RGB2LAB).astype(np.float32)
        
        # 计算统计量
        source_mean = np.mean(source_lab, axis=(0, 1))
        source_std = np.std(source_lab, axis=(0, 1)) + 1e-8
        target_mean = np.mean(target_lab, axis=(0, 1))
        target_std = np.std(target_lab, axis=(0, 1)) + 1e-8
        
        # 色彩迁移
        result_lab = source_lab.copy()
        for i in range(3):
            result_lab[:, :, i] = (source_lab[:, :, i] - source_mean[i]) * \
                                 (target_std[i] / source_std[i]) + target_mean[i]
        
        # 限制范围
        result_lab[:, :, 0] = np.clip(result_lab[:, :, 0], 0, 100)
        result_lab[:, :, 1] = np.clip(result_lab[:, :, 1], -127, 127)
        result_lab[:, :, 2] = np.clip(result_lab[:, :, 2], -127, 127)
        
        # 转换回RGB
        result_rgb = cv2.cvtColor(result_lab.astype(np.uint8), cv2.COLOR_LAB2RGB)
        
        return result_rgb
    
    def _simple_color_transfer(self, source: np.ndarray, target: np.ndarray) -> np.ndarray:
        """简单色彩迁移"""
        # 计算RGB通道的均值和标准差
        source_mean = np.mean(source, axis=(0, 1))
        source_std = np.std(source, axis=(0, 1)) + 1e-8
        target_mean = np.mean(target, axis=(0, 1))
        target_std = np.std(target, axis=(0, 1)) + 1e-8
        
        # 应用色彩迁移
        result = source.astype(np.float32)
        for i in range(3):
            result[:, :, i] = (result[:, :, i] - source_mean[i]) * \
                             (target_std[i] / source_std[i]) + target_mean[i]
        
        return np.clip(result, 0, 255).astype(np.uint8)

class BlendingProcessor:
    """混合处理器"""

    def __init__(self, config: EnhancedSwapConfigV2):
        self.config = config
        self.logger = get_logger("BlendingProcessor")

    def blend_faces(self, background: np.ndarray, foreground: np.ndarray,
                   mask: np.ndarray) -> np.ndarray:
        """混合人脸"""
        try:
            if self.config.blend_method == "alpha":
                return self._alpha_blending(background, foreground, mask)
            elif self.config.blend_method == "poisson":
                return self._poisson_blending(background, foreground, mask)
            elif self.config.blend_method == "seamless":
                return self._seamless_blending(background, foreground, mask)
            else:
                return self._alpha_blending(background, foreground, mask)

        except Exception as e:
            self.logger.warning(f"混合失败，使用alpha混合: {e}")
            return self._alpha_blending(background, foreground, mask)

    def _alpha_blending(self, background: np.ndarray, foreground: np.ndarray,
                       mask: np.ndarray) -> np.ndarray:
        """Alpha混合"""
        # 确保mask是3通道
        if len(mask.shape) == 2:
            mask = np.stack([mask] * 3, axis=2)

        # 应用混合强度
        alpha = mask * self.config.blend_strength

        # 羽化边缘
        if self.config.feather_amount > 0:
            kernel_size = self.config.feather_amount * 2 + 1
            alpha = cv2.GaussianBlur(alpha, (kernel_size, kernel_size), 0)

        # 混合
        result = background.astype(np.float32) * (1 - alpha) + \
                foreground.astype(np.float32) * alpha

        return np.clip(result, 0, 255).astype(np.uint8)

    def _poisson_blending(self, background: np.ndarray, foreground: np.ndarray,
                         mask: np.ndarray) -> np.ndarray:
        """泊松混合"""
        try:
            # 确保mask是单通道
            if len(mask.shape) == 3:
                mask = cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY)

            mask_uint8 = (mask * 255).astype(np.uint8)

            # 找到掩码中心
            moments = cv2.moments(mask_uint8)
            if moments['m00'] != 0:
                center_x = int(moments['m10'] / moments['m00'])
                center_y = int(moments['m01'] / moments['m00'])
            else:
                center_x, center_y = mask.shape[1]//2, mask.shape[0]//2

            # 使用OpenCV的seamlessClone
            result = cv2.seamlessClone(
                foreground, background, mask_uint8,
                (center_x, center_y), cv2.NORMAL_CLONE
            )

            return result

        except Exception as e:
            self.logger.warning(f"泊松混合失败，回退到alpha混合: {e}")
            return self._alpha_blending(background, foreground, mask)

    def _seamless_blending(self, background: np.ndarray, foreground: np.ndarray,
                          mask: np.ndarray) -> np.ndarray:
        """无缝混合"""
        # 多层次混合
        result = background.astype(np.float32)

        # 创建多个尺度的掩码
        scales = [1.0, 0.8, 0.6]
        weights = [0.5, 0.3, 0.2]

        for scale, weight in zip(scales, weights):
            if scale < 1.0:
                # 缩小掩码
                h, w = mask.shape[:2]
                new_h, new_w = int(h * scale), int(w * scale)
                scaled_mask = cv2.resize(mask, (new_w, new_h))
                scaled_mask = cv2.resize(scaled_mask, (w, h))
            else:
                scaled_mask = mask

            # 确保是3通道
            if len(scaled_mask.shape) == 2:
                scaled_mask = np.stack([scaled_mask] * 3, axis=2)

            # 应用加权混合
            blend_strength = self.config.blend_strength * weight
            alpha = scaled_mask * blend_strength

            result = result * (1 - alpha) + foreground.astype(np.float32) * alpha

        return np.clip(result, 0, 255).astype(np.uint8)

    def create_face_mask(self, face_landmarks: np.ndarray, img_shape: Tuple[int, int]) -> np.ndarray:
        """创建人脸掩码"""
        try:
            h, w = img_shape[:2]
            mask = np.zeros((h, w), dtype=np.uint8)

            if len(face_landmarks) >= 5:
                # 基于5点关键点创建椭圆掩码
                center_x = np.mean(face_landmarks[:, 0])
                center_y = np.mean(face_landmarks[:, 1])

                # 计算椭圆参数
                width = np.max(face_landmarks[:, 0]) - np.min(face_landmarks[:, 0])
                height = np.max(face_landmarks[:, 1]) - np.min(face_landmarks[:, 1])

                # 扩展掩码
                width = int(width * 1.3)
                height = int(height * 1.5)

                cv2.ellipse(mask, (int(center_x), int(center_y)),
                           (width//2, height//2), 0, 0, 360, 255, -1)
            else:
                # 如果关键点不足，创建中心掩码
                center_x, center_y = w//2, h//2
                radius = min(w, h) // 3
                cv2.circle(mask, (center_x, center_y), radius, 255, -1)

            # 羽化边缘
            if self.config.feather_amount > 0:
                kernel_size = self.config.feather_amount * 2 + 1
                mask = cv2.GaussianBlur(mask, (kernel_size, kernel_size), 0)

            return mask.astype(np.float32) / 255.0

        except Exception as e:
            self.logger.warning(f"创建人脸掩码失败: {e}")
            # 返回默认掩码
            h, w = img_shape[:2]
            mask = np.zeros((h, w), dtype=np.float32)
            center_x, center_y = w//2, h//2
            radius = min(w, h) // 4
            cv2.circle(mask, (center_x, center_y), radius, 1.0, -1)
            return mask

class EnhancedFaceSwapperV2:
    """增强换脸处理器 V2.0 - 避免递归调用"""

    def __init__(self, config: EnhancedSwapConfigV2 = None):
        self.config = config or EnhancedSwapConfigV2()
        self.logger = get_logger("EnhancedFaceSwapperV2")

        # 初始化子处理器
        self.face_enhancer = FaceEnhancementProcessor(self.config)
        self.color_matcher = ColorMatchingProcessor(self.config)
        self.blender = BlendingProcessor(self.config)

        # 性能统计
        self.stats = {
            'total_processed': 0,
            'enhanced_processed': 0,
            'avg_processing_time': 0.0,
            'last_processing_time': 0.0
        }

        self.logger.info(f"增强换脸处理器V2初始化完成 - 质量模式: {self.config.quality_mode}")

    def should_use_enhancement(self, img_shape: Tuple[int, ...], quality_mode: str = None) -> bool:
        """判断是否应该使用增强功能"""
        if not self.config.enabled:
            return False

        # 检查质量模式
        mode = quality_mode or self.config.quality_mode
        if mode == "fast":
            return False

        # 检查图像分辨率
        if len(img_shape) >= 2:
            height, width = img_shape[:2]
            if height < 128 or width < 128:
                return False

        # 检查GPU内存（如果可用）
        try:
            if torch.cuda.is_available():
                memory_usage = torch.cuda.memory_allocated() / torch.cuda.max_memory_allocated()
                if memory_usage > 0.9:
                    return False
        except:
            pass

        return True

    def process_face_swap(self, target_img: np.ndarray, swapped_face: np.ndarray,
                         face_landmarks: np.ndarray, transform_matrix: np.ndarray = None,
                         quality_mode: str = None) -> np.ndarray:
        """
        处理人脸交换的增强功能

        Args:
            target_img: 目标图像
            swapped_face: 已交换的人脸
            face_landmarks: 人脸关键点
            transform_matrix: 变换矩阵
            quality_mode: 质量模式

        Returns:
            增强后的图像
        """
        start_time = time.time()

        try:
            # 检查是否使用增强功能
            if not self.should_use_enhancement(target_img.shape, quality_mode):
                self.logger.debug("跳过增强处理")
                return swapped_face

            # 1. 人脸质量增强
            enhanced_face = self.face_enhancer.enhance_image_quality(swapped_face)

            # 2. 皮肤平滑
            enhanced_face = self.face_enhancer.apply_skin_smoothing(enhanced_face)

            # 3. 色彩匹配
            if target_img is not None:
                enhanced_face = self.color_matcher.match_colors(enhanced_face, target_img)

            # 4. 创建混合掩码
            mask = self.blender.create_face_mask(face_landmarks, target_img.shape)

            # 5. 智能混合
            result = self.blender.blend_faces(target_img, enhanced_face, mask)

            # 更新统计
            processing_time = time.time() - start_time
            self._update_stats(processing_time, True)

            return result

        except Exception as e:
            self.logger.warning(f"增强处理失败，返回原始结果: {e}")
            processing_time = time.time() - start_time
            self._update_stats(processing_time, False)
            return swapped_face

    def _update_stats(self, processing_time: float, enhanced: bool):
        """更新统计信息"""
        self.stats['total_processed'] += 1
        self.stats['last_processing_time'] = processing_time

        if enhanced:
            self.stats['enhanced_processed'] += 1

        # 更新平均处理时间
        total = self.stats['total_processed']
        self.stats['avg_processing_time'] = (
            self.stats['avg_processing_time'] * (total - 1) + processing_time
        ) / total

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        if stats['total_processed'] > 0:
            stats['enhancement_rate'] = stats['enhanced_processed'] / stats['total_processed']
        else:
            stats['enhancement_rate'] = 0.0
        return stats

    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        try:
            for key, value in new_config.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)

            self.logger.info("增强换脸配置已更新")

        except Exception as e:
            self.logger.error(f"更新配置失败: {e}")
