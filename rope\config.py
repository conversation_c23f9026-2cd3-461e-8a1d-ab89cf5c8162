"""
配置文件，用于存储全局配置变量
"""
from pathlib import Path
from typing import Dict, Any, Union, Optional
import json
import yaml
from dataclasses import dataclass, asdict
import logging

# 软件版本
SOFTWARE_VERSION = "stellar"

@dataclass
class ProcessingConfig:
    """处理相关配置"""
    min_resolution: int = 512
    max_resolution: int = 2048
    tile_size: int = 512
    max_faces: int = 20
    detection_score_threshold: float = 0.5
    similarity_threshold: float = 0.6
    thread_count: int = 4
    gpu_memory_fraction: float = 0.8

@dataclass
class UIConfig:
    """界面相关配置"""
    window_width: int = 1200
    window_height: int = 800
    language: str = "Chinese"
    theme: str = "dark"
    auto_save_interval: int = 300  # 秒

@dataclass
class ModelConfig:
    """模型相关配置"""
    model_cache_size: int = 5
    auto_load_models: bool = True
    preferred_provider: str = "CUDA"
    model_precision: str = "fp16"

class ConfigManager:
    """统一配置管理器"""

    def __init__(self, config_dir: Union[str, Path] = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        self.logger = logging.getLogger(__name__)

        # 配置文件映射
        self.config_files = {
            'processing': self.config_dir / 'processing.yaml',
            'ui': self.config_dir / 'ui.yaml',
            'models': self.config_dir / 'models.yaml',
            'app': self.config_dir / 'app.json'  # 保持向后兼容
        }

        # 配置类映射
        self.config_classes = {
            'processing': ProcessingConfig,
            'ui': UIConfig,
            'models': ModelConfig
        }

        self._configs = {}
        self._load_all_configs()

    def _load_all_configs(self):
        """加载所有配置"""
        for config_name, config_class in self.config_classes.items():
            try:
                self._configs[config_name] = self.load_config(config_name, config_class)
            except Exception as e:
                self.logger.warning(f"加载配置 {config_name} 失败: {e}")
                self._configs[config_name] = config_class()

    def load_config(self, config_name: str, config_class=None) -> Any:
        """加载指定配置"""
        config_path = self.config_files.get(config_name)
        if not config_path:
            raise ValueError(f"未知的配置类型: {config_name}")

        if not config_path.exists():
            # 创建默认配置
            if config_class:
                default_config = config_class()
                self.save_config(config_name, default_config)
                return default_config
            return {}

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix == '.yaml':
                    data = yaml.safe_load(f) or {}
                else:
                    data = json.load(f)

            if config_class:
                return config_class(**data)
            return data

        except Exception as e:
            self.logger.error(f"读取配置文件 {config_path} 失败: {e}")
            if config_class:
                return config_class()
            return {}

    def save_config(self, config_name: str, config_data: Any):
        """保存配置"""
        config_path = self.config_files.get(config_name)
        if not config_path:
            raise ValueError(f"未知的配置类型: {config_name}")

        try:
            # 如果是dataclass，转换为字典
            if hasattr(config_data, '__dataclass_fields__'):
                data = asdict(config_data)
            else:
                data = config_data

            with open(config_path, 'w', encoding='utf-8') as f:
                if config_path.suffix == '.yaml':
                    yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
                else:
                    json.dump(data, f, ensure_ascii=False, indent=2)

            # 更新内存中的配置
            self._configs[config_name] = config_data

        except Exception as e:
            self.logger.error(f"保存配置文件 {config_path} 失败: {e}")

    def get_config(self, config_name: str) -> Any:
        """获取配置"""
        return self._configs.get(config_name)

    def update_config(self, config_name: str, **kwargs):
        """更新配置"""
        config = self._configs.get(config_name)
        if config and hasattr(config, '__dataclass_fields__'):
            # 更新dataclass字段
            for key, value in kwargs.items():
                if hasattr(config, key):
                    setattr(config, key, value)
            self.save_config(config_name, config)
        else:
            self.logger.warning(f"无法更新配置 {config_name}")

# 全局配置管理器实例
config_manager = ConfigManager()