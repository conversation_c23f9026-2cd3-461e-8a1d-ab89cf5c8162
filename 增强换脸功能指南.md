
# 增强换脸功能 V2.0 用户指南

## 🎯 功能特点

### 质量模式
- **快速模式**: 最快速度，使用原始算法
- **标准模式**: 平衡速度和质量，启用基础增强
- **高质量模式**: 更好的质量，使用泊松混合
- **极致质量模式**: 最佳质量，包含超分辨率

### 增强功能
1. **人脸质量增强**: 自适应直方图均衡化 + 锐化
2. **皮肤平滑**: 智能皮肤检测 + 双边滤波
3. **色彩匹配**: 直方图匹配 / LAB色彩迁移
4. **智能混合**: Alpha / 泊松 / 无缝混合

## 🚀 使用方法

### 基础使用
1. 启动程序
2. 选择摄像头或视频
3. 加载源人脸图片
4. 选择质量模式（在设置中）
5. 启用换脸功能

### 高级设置
编辑 `config/enhanced_swap_v2.json`:

```json
{
  "UseEnhancedSwap": true,
  "QualityMode": "normal",
  "blend_method": "alpha",
  "blend_strength": 0.85,
  "enable_color_matching": true
}
```

## ⚙️ 配置参数

### 基础设置
- `UseEnhancedSwap`: 启用增强功能
- `QualityMode`: 质量模式 (fast/normal/high/ultra)

### 混合设置
- `blend_method`: 混合方法 (alpha/poisson/seamless)
- `blend_strength`: 混合强度 (0.0-1.0)
- `feather_amount`: 羽化程度 (0-30)

### 增强设置
- `enable_face_enhancement`: 启用人脸增强
- `enable_skin_smoothing`: 启用皮肤平滑
- `enable_color_matching`: 启用色彩匹配

## 🔧 故障排除

### 常见问题
1. **增强功能不工作**: 检查 `UseEnhancedSwap` 设置
2. **处理速度慢**: 降低质量模式或关闭超分辨率
3. **效果不自然**: 调整混合强度和羽化参数
4. **色彩不匹配**: 尝试不同的色彩迁移方法

### 性能优化
- 使用 `fast` 模式获得最快速度
- 关闭不需要的增强功能
- 降低目标分辨率
- 启用GPU优化

## 📊 统计信息

程序会自动收集性能统计:
- 总处理次数
- 增强处理次数
- 平均处理时间
- 增强成功率

可通过控制台查看详细统计信息。
