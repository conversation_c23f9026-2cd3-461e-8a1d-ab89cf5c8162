# 模型相关配置
# Model Configuration

# 模型缓存设置
model_cache_size: 5
auto_load_models: true
preload_common_models: true

# 推理设置
preferred_provider: "CUDA"  # CUDA, TensorRT, CPU
model_precision: "fp16"  # fp32, fp16
enable_tensorrt_optimization: true

# 模型路径设置
models_directory: "models"
dfl_models_directory: "dfl_models"
custom_models_directory: "custom_models"

# 自动下载设置
auto_download_missing_models: false
model_download_timeout: 300  # 秒
verify_model_checksums: true

# 模型优化设置
enable_model_quantization: false
optimize_for_inference: true
batch_size_optimization: true

# 常用模型列表
preload_models:
  - "inswapper_128.fp16.onnx"
  - "GFPGAN_model.onnx"
  - "det_10g.onnx"
  - "w600k_r50.onnx"

# 模型别名
model_aliases:
  face_swap: "inswapper_128.fp16.onnx"
  face_enhance: "GFPGAN_model.onnx"
  face_detect: "det_10g.onnx"
  face_recognize: "w600k_r50.onnx"

# 性能设置
max_model_memory_usage: 0.8  # GPU内存使用比例
model_load_timeout: 60  # 秒
enable_model_warmup: true

# 错误处理
retry_failed_loads: 3
fallback_to_cpu: true
ignore_missing_models: false
