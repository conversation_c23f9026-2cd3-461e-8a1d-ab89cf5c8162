import os
import sys
import cv2

def get_media_path(filename):
    """获取媒体文件的绝对路径"""
    if getattr(sys, 'frozen', False):
        base_path = sys._MEIPASS
    else:
        base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    return os.path.join(base_path, 'rope', 'media', filename)

def get_model_path(filename):
    """获取模型文件的绝对路径"""
    if getattr(sys, 'frozen', False):
        exe_dir = os.path.dirname(sys.executable)
        model_path = os.path.join(exe_dir, 'models', filename)
        print(f"Executable directory: {exe_dir}")
        print(f"Full model path: {model_path}")
        return model_path
    else:
        # 开发环境的路径
        base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        return os.path.join(base_path, 'models', filename)
DEFAULT_DATA = {
# Buttons
'AddMarkerButtonDisplay':           'icon',
'AddMarkerButtonIconHover':            get_media_path('add_marker_hover.png'),
'AddMarkerButtonIconOff':              get_media_path('add_marker_off.png'),
'AddMarkerButtonIconOn':               get_media_path('add_marker_off.png'),
'AddMarkerButtonInfoText':             '添加标记:\n将参数标记附加到当前帧. 标记回复制所有参数设置，并将其应用于所有未来帧，或者直到遇到其他标记.',
'AddMarkerButtonState':           False,

'SaveMarkerButtonDisplay':           'icon',
'SaveMarkerButtonIconHover':            get_media_path('marker_save.png'),
'SaveMarkerButtonIconOff':              get_media_path('marker_save.png'),
'SaveMarkerButtonIconOn':               get_media_path('marker_save.png'),
'SaveMarkerButtonInfoText':             '保存标记:\n保存此源视频的标记. 标记将作为 json 文件保存在与源视频相同的文件夹中.',
'SaveMarkerButtonState':           False,

'AudioDisplay':             'text',
'AudioInfoText':             '启用实时音频:\n在预览播放期间添加来自输入视频的音频. 如果无法保持输入视频帧速度，音频将滞后.',
'AudioState':               False,
'AudioText':                '启用音频',
'AutoSwapState':            False,
'ClearFacesDisplay':        'text',
'ClearFacesIcon':            get_media_path('tarfacedel.png'),
'ClearFacesIconHover':      get_media_path('rec.png'),
'ClearFacesIconOff':        get_media_path('rec.png'),
'ClearFacesIconOn':         get_media_path('rec.png'),
'ClearFacesInfoText':             '清脸:\n删除所有当前找到的脸.',
'ClearFacesState':          False,
'ClearFacesText':           '清脸',
'ClearmemState':            False,
'DefaultParamsButtonDisplay':           'text',
'DefaultParamsButtonInfoText':             '加载默认参数:\n加载此列的 StarPluck 默认参数.',
'DefaultParamsButtonState':           False,
'DefaultParamsButtonText':           '恢复默认',
'DelEmbedDisplay':          'text',
'DelEmbedIconHover':        get_media_path('rec.png'),
'DelEmbedIconOff':          get_media_path('rec.png'),
'DelEmbedIconOn':           get_media_path('rec.png'),
'DelEmbedInfoText':             '删除嵌入:\n删除当前选择的嵌入.',
'DelEmbedState':            False,
'DelEmbedText':             '删除嵌入',
'DelMarkerButtonDisplay':           'icon',
'DelMarkerButtonIconHover':            get_media_path('remove_marker_hover.png'),
'DelMarkerButtonIconOff':              get_media_path('remove_marker_off.png'),
'DelMarkerButtonIconOn':               get_media_path('remove_marker_off.png'),
'DelMarkerButtonInfoText':             '移除标记:\n从当前帧删除参数标记.',
'DelMarkerButtonState':           False,
'FindFacesDisplay':         'text',
'FindFacesIcon':         get_media_path('tarface.png'),
'FindFacesIconHover':       get_media_path('rec.png'),
'FindFacesIconOff':         get_media_path('rec.png'),
'FindFacesIconOn':          get_media_path('rec.png'),
'FindFacesInfoText':             '找脸:\n在当前帧查找所有新的人脸.',
'FindFacesState':           False,
'FindFacesText':            '找脸',
'ImgDockState':             False,
'ImgVidMode':               'Videos',
'ImgVidState':              False,
'LoadParamsButtonDisplay':           'text',
'LoadParamsButtonInfoText':             '加载保存的参数:\n加载此列中所有参数（如果之前已经保存）. ',
'LoadParamsButtonState':           False,
'LoadParamsButtonText':           '加载参数',
'LoadSFacesDisplay':         'both',
'LoadSFacesIcon':            get_media_path('save.png'),
'LoadSFacesIconHover':        get_media_path('save.png'),
'LoadSFacesIconOff':          get_media_path('save.png'),
'LoadSFacesIconOn':           get_media_path('save.png'),
'LoadSFacesInfoText':             '选择头像文件夹中人脸:\n从 Folder 选择并加载 Source Faces.确保改文件夹仅保护<good>图像.',
'LoadSFacesState':          False,
'LoadSFacesText':           '选择头像文件夹',
'LoadTVideosDisplay':         'both',
'LoadTVideosIconHover':        get_media_path('save.png'),
'LoadTVideosIconOff':          get_media_path('save.png'),
'LoadTVideosIconOn':           get_media_path('save.png'),
'LoadTVideosInfoText':             '选择输入视频/图像文件夹:\n从文件中选择并加载媒体.',
'LoadTVideosState':         False,
'LoadTVideosText':           '选择视频文件夹',
'MaskViewDisplay':         'text',
'MaskViewInfoText':             '显示蒙版:\n将脸部遮罩与脸部并排显示.有助于理解蒙版行为和结果.',
'MaskViewState':            False,
'MaskViewText':           '显示遮罩',
'CompareViewDisplay':         'text',
'CompareViewInfoText':             '显示比较视图:\n并排显示交换后的脸部和原始脸部',
'CompareViewState':            False,
'CompareViewText':           '显示比较视图',
'NextMarkerButtonDisplay':           'icon',
'NextMarkerButtonIconHover':            get_media_path('next_marker_hover.png'),
'NextMarkerButtonIconOff':              get_media_path('next_marker_off.png'),
'NextMarkerButtonIconOn':               get_media_path('next_marker_off.png'),
'NextMarkerButtonInfoText':             'NEXT MARKER:\nMove to the next marker.',
'NextMarkerButtonState':           False,
'OutputFolderDisplay':         'both',
'OutputFolderIconHover':        get_media_path('save.png'),
'OutputFolderIconOff':          get_media_path('save.png'),
'OutputFolderIconOn':           get_media_path('save.png'),
'OutputFolderInfoText':             '选择保存文件夹:\n选择用于保存视频和图像的文件夹.',
'OutputFolderState':        False,
'OutputFolderText':           '选择输出文件夹',
'PerfTestState':            False,
'PlayDisplay':              'icon',
'PlayIconHover':            get_media_path('play_hover.png'),
'PlayIconOff':              get_media_path('play_off.png'),
'PlayIconOn':               get_media_path('play_on.png'),
'PlayInfoText':             '播放:\n播放视频. 再按一次停止播放',
'PlayState':                False,
'PrevMarkerButtonDisplay':           'icon',
'PrevMarkerButtonIconHover':            get_media_path('previous_marker_hover.png'),
'PrevMarkerButtonIconOff':              get_media_path('previous_marker_off.png'),
'PrevMarkerButtonIconOn':               get_media_path('previous_marker_off.png'),
'PrevMarkerButtonInfoText':             '以前的标记:\n移动到上一个标记.',
'PrevMarkerButtonState':           False,
'RecordDisplay':            'icon',
'RecordIconHover':          get_media_path('rec_hover.png'),
'RecordIconOff':            get_media_path('rec_off.png'),
'RecordIconOn':             get_media_path('rec_on.png'),
'RecordInfoText': '录制:\n激活播放按钮进行录制。点击录制，然后点击播放开始录制。再次点击播放停止录制。',
'RecordState':              False,
'SaveImageState':           False,
'SaveParamsButtonDisplay':           'text',
'SaveParamsButtonInfoText':             '保存参数:\n保存此列中所有参数.',
'SaveParamsButtonState':            False,
'SaveParamsButtonText':             '保存参数',
'StartRopeDisplay':                 'both',
'StartRopeIconHover':               get_media_path('rope.png'),
'StartRopeIconOff':                 get_media_path('rope.png'),
'StartRopeIconOn':                  get_media_path('rope.png'),
'StartRopeInfoText':                '加载资源:\n加载视频、图片素材.',
'StartRopeState':                   False,
'StartRopeText':                    '加载资源',
'SwapFacesDisplay':                 'text',
'SwapFacesInfoText':                '替换:\n替换分配 Source Faces 和 Target Faces.',
'SwapFacesState':                   False,
'SwapFacesText':                    '换脸',
'EditFacesDisplay':                 'text',
'EditFacesInfoText':                '编辑:\n编辑人脸.',
'EditFacesState':                   False,
'EditFacesText':                    '编辑脸',
'EnhanceFrameDisplay':              'text',
'EnhanceFrameInfoText':             '增强:\n增强帧.',
'EnhanceFrameState':                False,
'EnhanceFrameText':                 '帧增强',
'TLBeginningDisplay':              'icon',
'TLBeginningIconHover':            get_media_path('tl_beg_hover.png'),
'TLBeginningIconOff':              get_media_path('tl_beg_off.png'),
'TLBeginningIconOn':               get_media_path('tl_beg_on.png'),
'TLBeginningInfoText':             '时间轴开始:\n将时间轴移动到第一帧.',
'TLBeginningState':                 False,
'TLLeftDisplay':                    'icon',
'TLLeftIconHover':                  get_media_path('tl_left_hover.png'),
'TLLeftIconOff':                    get_media_path('tl_left_off.png'),
'TLLeftIconOn':                     get_media_path('tl_left_on.png'),
'TLLeftInfoText':                   'TIMELEFT 向左轻移:\n将时间轴向左移动30帧.',
'TLLeftState':                      False,
'TLRightDisplay':                   'icon',
'TLRightIconHover':                 get_media_path('tl_right_hover.png'),
'TLRightIconOff':                   get_media_path('tl_right_off.png'),
'TLRightIconOn':                    get_media_path('tl_right_on.png'),
'TLRightInfoText':                  'TIMELEFT 向右轻移:\n将时间轴向右移动30帧.',
'TLRightState':                     False,

'SaveImageButtonDisplay':                   'text',
'SaveImageButtonInfoText':                  '保存图像:\n将图像保存到输出文件夹.',
'SaveImageButtonState':                     False,
'SaveImageButtonText':             '保存图像',

'AutoSwapButtonDisplay':                   'text',
'AutoSwapButtonInfoText':                  '自动替换:\n自动将输入脸部应用于新图像.',
'AutoSwapButtonState':                     False,
'AutoSwapButtonText':             '自动替换',

'ClearVramButtonDisplay':                   'text',
'ClearVramButtonInfoText':                  '清除 VRAM:\n从 VRAM 中清除模型（释放显存）.',
'ClearVramButtonState':                     False,
'ClearVramButtonText':             '释放显存',

'GetNewEmbButtonDisplay':                   'text',
'GetNewEmbButtonInfoText':                  '清除 VRAM:\n从 VRAM 中清除模型（释放显存）.',
'GetNewEmbButtonState':                     False,
'GetNewEmbButtonText':             '释放显存',

'StopMarkerButtonnDisplay':                   'icon',
'StopMarkerButtonIconHover':            get_media_path('previous_marker_hover.png'),
'StopMarkerButtonIconOff':              get_media_path('previous_marker_off.png'),
'StopMarkerButtonIconOn':               get_media_path('previous_marker_off.png'),
'StopMarkerButtonInfoText':                  '清除 VRAM:\n从 VRAM 中清除模型（释放显存）.',
'StopMarkerButtonState':                     False,
'StopMarkerButtonText':             '释放显存',

#AutoColor
'AutoColorSwitchInfoText':          '自动色彩转移类型. 11. 不带遮罩的 Hans 测试，2. 带遮罩的 Hans 测试，3. 不带遮罩的 DFL 方法，4. DFL 原始方法',
'AutoColorSwitchState':             False,

# Final Blur Switches
'FinalBlurSwitchInfoText':        '最终模糊开关:\n在管道末尾进行模糊',
'FinalBlurSwitchState':           False,

# Switches
'ColorSwitchInfoText':              'RGB 调整:\n微调替换的 RGB 颜色值.',
'ColorSwitchState':                 False,
'DiffSwitchInfoText':               '差异化:\n当两个图像之间差异较小时，允许一些原始面部显示在替换的结果中.可以帮助替换掉面部恢复一些纹理',
'DiffSwitchState':                  False,
'FaceAdjSwitchInfoText':            '输入面部调整:\n这是一项实验性功能，用于对探测器找到人脸特征点执行直接调整.还有一个选项可以调整替换掉人脸比例.',
'FaceAdjSwitchState':               False,
# Face Landmarks Detection
'LandmarksDetectionAdjSwitchInfoText': '地标检测 调整:\n这是一项实验性功能，用于对探测器找到人脸特征点执行直接调整. ',
'LandmarksDetectionAdjSwitchState':    False,
'LandmarksAlignModeFromPointsSwitchInfoText': '地标位置 调整对齐模式来自点:\n这是一项实验性功能，可对从探测器关键点找到人脸特征点执行直接调整.',
'LandmarksAlignModeFromPointsSwitchState':    False,
'ShowLandmarksSwitchInfoText':      '实时显示地标.',
'ShowLandmarksSwitchState':         False,
#
# Face Landmarks Position
'LandmarksPositionAdjSwitchInfoText': '地标位置调整:\n这是一项实验性功能，用于对探测器找到人脸特征点的位置执行直接调整 . ',
'LandmarksPositionAdjSwitchState':    False,
#
# Face Likeness
'FaceLikenessSwitchInfoText':       '人脸相似度:\n这是一项实验性功能，可直接调整人脸.',
'FaceLikenessSwitchState':           False,
#
# Auto Rotation
'AutoRotationSwitchInfoText':       '自动旋转:\n自动旋转帧以找到最佳检测角度',
'AutoRotationSwitchState':           False,
#
'FaceParserSwitchInfoText':         '人脸解析:\n解析背景和人脸.',
'FaceParserSwitchState':            False,
'MouthParserSwitchInfoText':        '嘴巴蒙版:\n让原始脸部的嘴巴显示在替换掉脸上.',
'MouthParserSwitchState':           False,
'OccluderSwitchInfoText':           '自动遮罩:\n自动检测面部的物体显示在替换的脸上.',
'OccluderSwitchState':              False,
'DFLRCTColorSwitchInfoText':        'DFL RCT 色彩转移:\n使用DFL替换人脸时使用RCT色彩转移',
'DFLRCTColorSwitchState':           False,
'DFLLoadOnlyOneSwitchInfoText':     '仅载入一个DFL模型:\n使用 DFL换脸时，内存中仅保留一个 DFL 模型',
'DFLLoadOnlyOneSwitchState':        False,
'DFLXSegSwitchInfoText':            'DFL 遮罩:\n允许遮挡脸部的物体出现在替换的图像中.',
'DFLXSegSwitchState':               False,
'OrientSwitchInfoText':             '方向:\n旋转人脸检测器以更好地检测不同角度的人脸',
'OrientSwitchState':                False,
'RestorerSwitchInfoText':           '人脸高清修复:\n通过放大模型高清修复人脸.',
'RestorerSwitchState':              False,
'Restorer2SwitchInfoText':           '面部修复:\n通过放大修复替换的图像.',
'Restorer2SwitchState':              False,
'StrengthSwitchInfoText':           'SWAPPER 强度:\n应用额外的替换迭代以增加结果强度，可增加相似度.',
'StrengthSwitchState':              False,
'CLIPSwitchInfoText':               '文本蒙版:\n通过文本描述来识别最终替换图像中出现的遮挡物.',
'CLIPSwitchState':                  False,

'VirtualCameraSwitchState':         False,
'VirtualCameraSwitchInfoText':      '虚拟摄像头:\n将替换后的图像发送到虚拟摄像头使用',

'RestoreEyesSwitchInfoText':        '保留眼睛: \n保留原始人脸的眼睛',
'RestoreEyesSwitchState':           False,
'RestoreMouthSwitchInfoText':       '保留嘴巴: \n保留原始人脸的嘴巴',
'RestoreMouthSwitchState':          False,

'JpegCompressionSwitchInfoText':     'Jpeg 脸部压缩:\n人脸的 Jpeg 压缩效果.',
'JpegCompressionSwitchState':        False,
# Sliders
'BlendSliderAmount':                5,
'BlendSliderInc':                   1,
'BlendSliderInfoText':              '整体遮罩混合:\n组合蒙版混合距离.',
'BlendSliderMax':                   100,
'BlendSliderMin':                   0,
'FinalBlurSliderAmount':           0,
'FinalBlurSliderInc':              1,
'FinalBlurSliderInfoText':         '边界遮罩混合:\n边界遮罩混合距离.',
'FinalBlurSliderMax':              50,
'FinalBlurSliderMin':              0,

'BorderBlurSliderAmount':           10,
'BorderBlurSliderInc':              1,
'BorderBlurSliderInfoText':         '边框模糊:\n边框遮罩混合距离.',
'BorderBlurSliderMax':              100,
'BorderBlurSliderMin':              0,
'BorderBottomSliderAmount':         10,
'BorderBottomSliderInc':            1,
'BorderBottomSliderInfoText':       '底部边框距离:\n一个具有可调节底部的矩形.',
'BorderBottomSliderMax':            100,
'BorderBottomSliderMin':            0,
'BorderLeftSliderAmount':           10,
'BorderLeftSliderInc':              1,
'BorderLeftSliderInfoText':         '左侧边框距离:\n一个具有可调节左侧的矩形.',
'BorderLeftSliderMax':              100,
'BorderLeftSliderMin':              0,
'BorderRightSliderAmount':          10,
'BorderRightSliderInc':             1,
'BorderRightSliderInfoText':        '右侧边框距离:\n一个具有可调节右侧的矩形.',
'BorderRightSliderMax':             100,
'BorderRightSliderMin':             0,
'BorderTopSliderAmount':            10,
'BorderTopSliderInc':               1,
'BorderTopSliderInfoText':          '顶部边框距离:\n一个具有可调节顶部的矩形.',
'BorderTopSliderMax':               100,
'BorderTopSliderMin':               0,
'AutoColorSliderAmount':            80,
'AutoColorSliderInc':               5,
'AutoColorSliderInfoText':          '自动混合颜色值',
'AutoColorSliderMax':               100,
'AutoColorSliderMin':               0,
'ColorBlueSliderAmount':            0,
'ColorBlueSliderInc':               1,
'ColorBlueSliderInfoText':          'RGB 蓝色调整',
'ColorBlueSliderMax':               100,
'ColorBlueSliderMin':               -100,
'ColorGreenSliderAmount':           0,
'ColorGreenSliderInc':              1,
'ColorGreenSliderInfoText':         'RGB 绿色调整',
'ColorGreenSliderMax':              100,
'ColorGreenSliderMin':              -100,
'ColorRedSliderAmount':             0,
'ColorRedSliderInc':                1,
'ColorRedSliderInfoText':           'RGB 红色调整',
'ColorRedSliderMax':                100,
'ColorRedSliderMin':                -100,
'DetectScoreSliderAmount':          50,
'DetectScoreSliderInc':             1,
'DetectScoreSliderInfoText':        '检测分数调整:\n确定要检测人脸所需的最低分数. 数值越高，人脸质量越高. 例如, 人脸在极端角度时闪烁，则提高此值将其限制替换.',
'DetectScoreSliderMax':             100,
'DetectScoreSliderMin':             1,
# Face Landmarks Detection
'LandmarksDetectScoreSliderAmount':  50,
'LandmarksDetectScoreSliderInc':     1,
'LandmarksDetectScoreSliderInfoText':'地标检测得分:\n确定人脸所需的最低分数被检测到. 更高的值需要更高质量的面. 例如, 如果面在极端角度时闪烁，则提高该值将限制交换尝试.',
'LandmarksDetectScoreSliderMax':     100,
'LandmarksDetectScoreSliderMin':     1,
# Jpeg Compression
'JpegCompressionSliderAmount':       50,
'JpegCompressionSliderInc':          1,
'JpegCompressionSliderInfoText':     '修复阈值:\n将恢复的结果混合回原始交换中.',
'JpegCompressionSliderMax':          100,
'JpegCompressionSliderMin':          1,
#
# Face Likeness
'FaceLikenessFactorSliderAmount':    0.00,
'FaceLikenessFactorSliderInc':       0.05,
'FaceLikenessFactorSliderInfoText':  '面部相似度:\n确定源脸和指定脸部之间的相似因子.',
'FaceLikenessFactorSliderMax':       1.00,
'FaceLikenessFactorSliderMin':       -1.00,
#
# Face Landmarks Position
'FaceIDSliderAmount':               1,
'FaceIDSliderInc':                  1,
'FaceIDSliderInfoText':             '面部位置人脸 ID:\n确定可以修改人脸点位置的目标人脸.',
'FaceIDSliderMax':                  20,
'FaceIDSliderMin':                  1,
'EyeLeftXSliderAmount':             0,
'EyeLeftXSliderInc':                1,
'EyeLeftXSliderInfoText':           '左眼 X 方向:\n左眼眼左右移动检测点',
'EyeLeftXSliderMax':                100,
'EyeLeftXSliderMin':                -100,
'EyeLeftYSliderAmount':             0,
'EyeLeftYSliderInc':                1,
'EyeLeftYSliderInfoText':           '左眼 Y 方向:\n左眼上下移动检测点',
'EyeLeftYSliderMax':                100,
'EyeLeftYSliderMin':                -100,
'EyeRightXSliderAmount':             0,
'EyeRightXSliderInc':                1,
'EyeRightXSliderInfoText':           '右眼 X 方向:\n右眼左右移动检测点',
'EyeRightXSliderMax':                100,
'EyeRightXSliderMin':                -100,
'EyeRightYSliderAmount':             0,
'EyeRightYSliderInc':                1,
'EyeRightYSliderInfoText':           '右眼 Y 方向:\n右眼上下移动检测点',
'EyeRightYSliderMax':                100,
'EyeRightYSliderMin':                -100,
'NoseXSliderAmount':                 0,
'NoseXSliderInc':                    1,
'NoseXSliderInfoText':               '鼻子 X 方向:\n鼻子左右移动检查点',
'NoseXSliderMax':                    100,
'NoseXSliderMin':                    -100,
'NoseYSliderAmount':                 0,
'NoseYSliderInc':                    1,
'NoseYSliderInfoText':               '鼻子 Y 方向:\n鼻子上下移动检查点',
'NoseYSliderMax':                    100,
'NoseYSliderMin':                    -100,
'MouthLeftXSliderAmount':            0,
'MouthLeftXSliderInc':               1,
'MouthLeftXSliderInfoText':          '嘴角左侧 X 方向:\n嘴角左侧左右移动检查点',
'MouthLeftXSliderMax':               100,
'MouthLeftXSliderMin':               -100,
'MouthLeftYSliderAmount':            0,
'MouthLeftYSliderInc':               1,
'MouthLeftYSliderInfoText':          '嘴角左侧 Y 方向:\n嘴角左侧上下移动检查点',
'MouthLeftYSliderMax':               100,
'MouthLeftYSliderMin':               -100,
'MouthRightXSliderAmount':           0,
'MouthRightXSliderInc':              1,
'MouthRightXSliderInfoText':         '嘴角右侧 X 方向:\n嘴角右侧左右移动检查点',
'MouthRightXSliderMax':              100,
'MouthRightXSliderMin':              -100,
'MouthRightYSliderAmount':           0,
'MouthRightYSliderInc':              1,
'MouthRightYSliderInfoText':         '嘴角右侧 Y 方向:\n嘴角右侧上下移动检查点',
'MouthRightYSliderMax':              100,
'MouthRightYSliderMin':              -100,
#
'DiffSliderAmount':                 4,
'DiffSliderInc':                    1,
'DiffSliderInfoText':               '差异量数值:\n值越高，相似性限制越宽松.',
'DiffSliderMax':                    100,
'DiffSliderMin':                    0,
'FaceParserSliderAmount':           0,
'FaceParserSliderInc':              1,
'FaceParserSliderInfoText':         '背景蒙版:\n负值/正值会缩小并放大.',
'FaceParserSliderMax':              50,
'FaceParserSliderMin':              -50,
'FaceScaleSliderAmount':            0,
'FaceScaleSliderInc':               1,
'FaceScaleSliderInfoText':          '面部缩放',
'FaceScaleSliderMax':               20,
'FaceScaleSliderMin':               -20,
'KPSScaleSliderAmount':             0,
'KPSScaleSliderInc':                1,
'KPSScaleSliderInfoText':           'KPS 缩放:\n增大或缩小检查点距离.',
'KPSScaleSliderMax':                100,
'KPSScaleSliderMin':                -100,
'KPSXSliderAmount':                 0,
'KPSXSliderInc':                    1,
'KPSXSliderInfoText':               'KPS X-方向:\n将检查点左右移动',
'KPSXSliderMax':                    100,
'KPSXSliderMin':                    -100,
'KPSYSliderAmount':                 0,
'KPSYSliderInc':                    1,
'KPSYSliderInfoText':               'KPS Y-方向:\n将检查点上下移动',
'KPSYSliderMax':                    100,
'KPSYSliderMin':                    -100,
'MouthParserSliderAmount':          0,
'MouthParserSliderInc':             1,
'MouthParserSliderInfoText':        '嘴巴遮罩:\n调整遮罩大小.遮住口腔内部，包括舌头.',
'MouthParserSliderMax':             30,
'MouthParserSliderMin':             0,

'NeckParserSliderAmount':          0,
'NeckParserSliderInc':             1,
'NeckParserSliderInfoText':        '颈部遮罩:\n调整蒙版的大小.',
'NeckParserSliderMax':             30,
'NeckParserSliderMin':             0,

'LeftEyeBrowParserSliderAmount':          0,
'LeftEyeBrowParserSliderInc':             1,
'LeftEyeBrowParserSliderInfoText':        '左眉:\n调整蒙版的大小.',
'LeftEyeBrowParserSliderMax':             30,
'LeftEyeBrowParserSliderMin':             0,

'RightEyeBrowParserSliderAmount':          0,
'RightEyeBrowParserSliderInc':             1,
'RightEyeBrowParserSliderInfoText':        '右眉:\n调整蒙版的大小.',
'RightEyeBrowParserSliderMax':             30,
'RightEyeBrowParserSliderMin':             0,

'LeftEyeParserSliderAmount':          0,
'LeftEyeParserSliderInc':             1,
'LeftEyeParserSliderInfoText':        '左眼:\n调整蒙版的大小.',
'LeftEyeParserSliderMax':             30,
'LeftEyeParserSliderMin':             0,

'RightEyeParserSliderAmount':          0,
'RightEyeParserSliderInc':             1,
'RightEyeParserSliderInfoText':        '右眼:\n调整蒙版的大小.',
'RightEyeParserSliderMax':             30,
'RightEyeParserSliderMin':             0,

'NoseParserSliderAmount':          0,
'NoseParserSliderInc':             1,
'NoseParserSliderInfoText':        '鼻子:\n调整蒙版的大小.',
'NoseParserSliderMax':             30,
'NoseParserSliderMin':             0,

'UpperLipParserSliderAmount':          0,
'UpperLipParserSliderInc':             1,
'UpperLipParserSliderInfoText':        '上唇:\n调整蒙版的大小.',
'UpperLipParserSliderMax':             30,
'UpperLipParserSliderMin':             0,

'LowerLipParserSliderAmount':          0,
'LowerLipParserSliderInc':             1,
'LowerLipParserSliderInfoText':        '下唇:\n调整蒙版的大小.',
'LowerLipParserSliderMax':             30,
'LowerLipParserSliderMin':             0,

'RestoreEyesSliderAmount':               50,
'RestoreEyesSliderInc':                  1,
'RestoreEyesSliderInfoText':             '眼睛混合 :\n增加显示替换的眼睛. 减少显示素材的眼睛',
'RestoreEyesSliderMax':                  100,
'RestoreEyesSliderMin':                  1,

'RestoreEyesSizeSliderAmount':               3,
'RestoreEyesSizeSliderInc':                  0.5,
'RestoreEyesSizeSliderInfoText':             '眼睛区域大小 :\n在交换缩小的画面时减少此限制.',
'RestoreEyesSizeSliderMax':                  4,
'RestoreEyesSizeSliderMin':                  2,

'Eyes_Mouth_BlurSliderAmount':               0,
'Eyes_Mouth_BlurSliderInc':                  1,
'Eyes_Mouth_BlurSliderInfoText':             '眼睛和嘴巴遮罩模糊 :\n调整蒙版边框的模糊度',
'Eyes_Mouth_BlurSliderMax':                  50,
'Eyes_Mouth_BlurSliderMin':                  0,
'RestoreEyesFeatherSliderAmount':               10,
'RestoreEyesFeatherSliderInc':                  1,
'RestoreEyesFeatherSliderInfoText':             '眼睛边缘羽化 :\n羽化眼睛边缘使其更贴合',
'RestoreEyesFeatherSliderMax':                  100,
'RestoreEyesFeatherSliderMin':                  1,

'RestoreEyesRadiusFactorXSliderAmount':      1.0,
'RestoreEyesRadiusFactorXSliderInc':         0.1,
'RestoreEyesRadiusFactorXSliderInfoText':    'X 眼睛半径系数 :\n这些参数确定蒙版的形状。如果两者都等于 1.0，则蒙版将为圆形。如果其中任何一个大于或小于 1.0，蒙版将变为椭圆形，并沿相���方向拉伸或收缩.',
'RestoreEyesRadiusFactorXSliderMax':         3.0,
'RestoreEyesRadiusFactorXSliderMin':         0.3,

'RestoreEyesRadiusFactorYSliderAmount':      1.0,
'RestoreEyesRadiusFactorYSliderInc':         0.1,
'RestoreEyesRadiusFactorYSliderInfoText':    'Y 眼睛半径系数 :\n这些参数确定蒙版的形状。如果两者都等于 1.0，则蒙版将为圆形。如果其中任何一个大于或小于 1.0，蒙版将变为椭圆形，并沿相应方向拉伸或收缩.',
'RestoreEyesRadiusFactorYSliderMax':         3.0,
'RestoreEyesRadiusFactorYSliderMin':         0.3,

'RestoreEyesXoffsetSliderAmount':      0,
'RestoreEyesXoffsetSliderInc':         1,
'RestoreEyesXoffsetSliderInfoText':    'X 修复眼睛偏移滑块 :\n移动 X 轴上的遮罩',
'RestoreEyesXoffsetSliderMax':         300,
'RestoreEyesXoffsetSliderMin':         -300,
'RestoreEyesYoffsetSliderAmount':      0,
'RestoreEyesYoffsetSliderInc':         1,
'RestoreEyesYoffsetSliderInfoText':    'Y 修复眼睛偏移滑块 :\n移动 Y 轴上的遮罩',
'RestoreEyesYoffsetSliderMax':         300,
'RestoreEyesYoffsetSliderMin':         -300,
'RestoreEyesSpacingOffsetSliderAmount':      0,
'RestoreEyesSpacingOffsetSliderInc':         1,
'RestoreEyesSpacingOffsetSliderInfoText':    'Y 修复眼睛间距偏移滑块 :\n改变双眼之间的距离',
'RestoreEyesSpacingOffsetSliderMax':         200,
'RestoreEyesSpacingOffsetSliderMin':         -200,
'RestoreMouthSliderAmount':               50,
'RestoreMouthSliderInc':                  1,
'RestoreMouthSliderInfoText':             '嘴巴混合 :\n增加此项可显示更多已交换的 Mouth。减小它以显示更多原始 Mouth',
'RestoreMouthSliderMax':                  100,
'RestoreMouthSliderMin':                  1,

'RestoreMouthSizeSliderAmount':               25,
'RestoreMouthSizeSliderInc':                  5,
'RestoreMouthSizeSliderInfoText':             '嘴巴区域大小 :\n在交换缩小的画面中的人脸时增加此项.',
'RestoreMouthSizeSliderMax':                  60,
'RestoreMouthSizeSliderMin':                  5,

'RestoreMouthFeatherSliderAmount':               10,
'RestoreMouthFeatherSliderInc':                  1,
'RestoreMouthFeatherSliderInfoText':             '嘴巴边缘羽化 :\n调整 Mouth blending （嘴部混合） 的边框。增加此选项可显示更多原始 Mouth。减小此选项可显示更多已交换的 Mouth',
'RestoreMouthFeatherSliderMax':                  100,
'RestoreMouthFeatherSliderMin':                  1,

'RestoreMouthRadiusFactorXSliderAmount':      1.0,
'RestoreMouthRadiusFactorXSliderInc':         0.1,
'RestoreMouthRadiusFactorXSliderInfoText':    'X 嘴巴半径系数 :\n这些参数决定了掩模的形状。如果两者都等于1.0，则掩码将是圆形的。如果任一值大于或小于1.0，则掩模将变为椭圆形，沿相应方向拉伸或收缩.',
'RestoreMouthRadiusFactorXSliderMax':         3.0,
'RestoreMouthRadiusFactorXSliderMin':         0.3,

'RestoreMouthRadiusFactorYSliderAmount':      1.0,
'RestoreMouthRadiusFactorYSliderInc':         0.1,
'RestoreMouthRadiusFactorYSliderInfoText':    'Y 嘴巴半径系数 :\n这些参数决定了掩模的形状。如果两者都等于1.0，则掩码将是圆形的。如果任一值大于或小于1.0，则掩模将变为椭圆形，沿相应方向拉伸或收缩.',
'RestoreMouthRadiusFactorYSliderMax':         3.0,
'RestoreMouthRadiusFactorYSliderMin':         0.3,

'RestoreMouthXoffsetSliderAmount':      0,
'RestoreMouthXoffsetSliderInc':         1,
'RestoreMouthXoffsetSliderInfoText':    'X 修复嘴角偏移滑块 :\n移动 X 轴上的嘴部遮罩',
'RestoreMouthXoffsetSliderMax':         300,
'RestoreMouthXoffsetSliderMin':         -300,
'RestoreMouthYoffsetSliderAmount':      0,
'RestoreMouthYoffsetSliderInc':         1,
'RestoreMouthYoffsetSliderInfoText':    'Y 修复嘴角偏移滑块 :\n移动 Y 轴上的嘴部遮罩',
'RestoreMouthYoffsetSliderMax':         300,
'RestoreMouthYoffsetSliderMin':         -300,
'OccluderSliderAmount':             0,
'OccluderSliderInc':                1,
'OccluderSliderInfoText':           '自动遮罩大小:\n扩大或缩小遮罩区域',
'OccluderSliderMax':                100,
'OccluderSliderMin':                -100,
'DFLXSegSliderAmount':             0,
'DFLXSegSliderInc':                1,
'DFLXSegSliderInfoText':           'DFL 遮罩大小:\n扩大或缩小遮罩区域',
'DFLXSegSliderMax':                100,
'DFLXSegSliderMin':                -100,
'OccluderBlurSliderAmount':             5,
'OccluderBlurSliderInc':                1,
'OccluderBlurSliderInfoText':           '遮挡器模糊度:\n遮挡器和 XSeg 的混合值',
'OccluderBlurSliderMax':                100,
'OccluderBlurSliderMin':                0,
'ParserBlurSliderAmount':             5,
'ParserBlurSliderInc':                1,
'ParserBlurSliderInfoText':           'FaceParser 模糊度:\n FaceParser 的混合值',
'ParserBlurSliderMax':                100,
'ParserBlurSliderMin':                0,
'BGParserBlurSliderAmount':             5,
'BGParserBlurSliderInc':                1,
'BGParserBlurSliderInfoText':           'BG FaceParser 模糊度:\n BG FaceParser的混合值',
'BGParserBlurSliderMax':                100,
'BGParserBlurSliderMin':                0,
'DiffingBlurSliderAmount':                5,
'DiffingBlurSliderInc':                   1,
'DiffingBlurSliderInfoText':              '比较的混合值:\n 混合差异值.',
'DiffingBlurSliderMax':                   100,
'DiffingBlurSliderMin':                   0,
'OrientSliderAmount':               0,
'OrientSliderInc':                  90,
'OrientSliderInfoText':             '角度:\n将此设置为输入面角度的角度，以帮助放置/倒置/等。顺时针读取角度.',
'OrientSliderMax':                  270,
'OrientSliderMin':                  0,
'RestorerSliderAmount':             100,
'RestorerSliderInc':                5,
'RestorerSliderInfoText':           '高清修复混合度:\n调整大小混合人脸修复.',
'RestorerSliderMax':                100,
'RestorerSliderMin':                0,
'Restorer2SliderAmount':             100,
'Restorer2SliderInc':                5,
'Restorer2SliderInfoText':           '2. 修复程度:\n将恢复的结果混合回原始交换中.',
'Restorer2SliderMax':                100,
'Restorer2SliderMin':                0,
'EnhancerSliderAmount':             100,
'EnhancerSliderInc':                5,
'EnhancerSliderInfoText':           '帧混合度:\n将增强的结果混合回原始帧.',
'EnhancerSliderMax':                100,
'EnhancerSliderMin':                0,
'StrengthSliderAmount':             100,
'StrengthSliderInc':                25,
'StrengthSliderInfoText':           '增强度数值:\n增加高达5倍的额外掉期（500%）。200%通常是一个不错的结果。设置为0可关闭交换，但允许管道的其余部分应用于原始图像.',
'StrengthSliderMax':                500,
'StrengthSliderMin':                0,
'ThreadsSliderAmount':              5,
'ThreadsSliderInc':                 1,
'ThreadsSliderInfoText':            '执行线程:\n设置播放和录制时的执行线程数。强烈依赖于GPU VRAM。24GB有5个线程.',
'ThreadsSliderMax':                 50,
'ThreadsSliderMin':                 1,
'ThresholdSliderAmount':            55,
'ThresholdSliderInc':               1,
'ThresholdSliderInfoText':          '相似度阀值:\n提高以减少交换多人时的面部跳跃。值越高，要求越严格.',
'ThresholdSliderMax':               100,
'ThresholdSliderMin':               0,
'VideoQualSliderAmount':            18,
'VideoQualSliderInc':               1,
'VideoQualSliderInfoText':          '视频质量:\n录制视频的编码质量。0是最好的，50是最差的，18大多是无损的。文件大小随着质量数字的降低而增加.',
'VideoQualSliderMax':               50,
'VideoQualSliderMin':               0,

'DFLAmpMorphSliderAmount':          50,
'DFLAmpMorphSliderInc':             1,
'DFLAmpMorphSliderInfoText':        ' DFL 变形因子\n:使用DFL AMP模型时设置变形因子',
'DFLAmpMorphSliderMax':             100,
'DFLAmpMorphSliderMin':             1,

'AudioSpeedSliderAmount':           1.00,
'AudioSpeedSliderInc':              0.01,
'AudioSpeedSliderInfoText':         '音频播放速度:\n打开"启用音频"时播放Audo',
'AudioSpeedSliderMax':              2.00,
'AudioSpeedSliderMin':              0.50,

'VQFRFidelitySliderAmount':         0.0,
'VQFRFidelitySliderInc':            0.1,
'VQFRFidelitySliderInfoText':       '保真度比率:\nVQFR v2恢复器的保真度比值.',
'VQFRFidelitySliderMax':            1.0,
'VQFRFidelitySliderMin':            0.0,

'CLIPSliderAmount':                 50,
'CLIPSliderInc':                    1,
'CLIPSliderInfoText':               '文本遮罩数值:\n增加以增强效果.',
'CLIPSliderMax':                    100,
'CLIPSliderMin':                    0,

'ColorGammaSliderAmount':                 1,
'ColorGammaSliderInc':                    0.02,
'ColorGammaSliderInfoText':               '伽马值:\n更改伽马.',
'ColorGammaSliderMax':                    2,
'ColorGammaSliderMin':                    0,

'NoiseSliderAmount':                 0,
'NoiseSliderInc':                    0.5,
'NoiseSliderInfoText':               '伽马值:\n 为交换后的脸部添加噪点',
'NoiseSliderMax':                    20,
'NoiseSliderMin':                    0,
'ColorBrightSliderAmount':                 1,
'ColorBrightSliderInc':                    0.01,
'ColorBrightSliderInfoText':               '亮度:\n明亮变化.',
'ColorBrightSliderMax':                    2,
'ColorBrightSliderMin':                    0,

'ColorContrastSliderAmount':                 1,
'ColorContrastSliderInc':                    0.01,
'ColorContrastSliderInfoText':               '对比度:\n更改对比度.',
'ColorContrastSliderMax':                    2,
'ColorContrastSliderMin':                    0,

'ColorSaturationSliderAmount':                 1,
'ColorSaturationSliderInc':                    0.01,
'ColorSaturationSliderInfoText':               '饱和度:\n更改饱和度.',
'ColorSaturationSliderMax':                    2,
'ColorSaturationSliderMin':                    0,

'ColorSharpnessSliderAmount':                 1,
'ColorSharpnessSliderInc':                    0.1,
'ColorSharpnessSliderInfoText':               '锐化:\n更改清晰度.',
'ColorSharpnessSliderMax':                    2,
'ColorSharpnessSliderMin':                    0,

'ColorHueSliderAmount':                 0,
'ColorHueSliderInc':                    0.01,
'ColorHueSliderInfoText':               '色调:\n更改色调.',
'ColorHueSliderMax':                    0.5,
'ColorHueSliderMin':                    -0.5,

# Text Selection
'DetectTypeTextSelInfoText':        '人脸检测类型模型:\n选择面部检测模型。大多只是细微的差异，但当面部处于极端角度或被遮挡时，可能会出现显著差异.',
'DetectTypeTextSelMode':            'Retinaface',
'DetectTypeTextSelModes':           ['Retinaface', 'Yolov8', 'SCRDF', 'Yunet'],
# Face Landmarks Detection
'LandmarksDetectTypeTextSelInfoText': '地标检测类型模型:\n选择地标人脸检测模型。大多数情况下只有细微的差异，但当面部处于极端角度或被遮挡时，差异可能会很大。',
'LandmarksDetectTypeTextSelMode':     '203',
'LandmarksDetectTypeTextSelModes':    ['5', '68', '3d68', '98', '106', '203', '478'],
#
# Similarity Type
'SimilarityTypeTextSelInfoText':    '相似类型:\n选择要与弧面识别器模型一起使用的相似性.',
'SimilarityTypeTextSelMode':        '蛋白石',
'SimilarityTypeTextSelModes':       ['蛋白石', '珍珠', '最佳'],
#
# ProvidersPriority
'ProvidersPriorityTextSelInfoText':    '提供者优先级:\n请选择系统运行时使用的计算提供者优先级。',
'ProvidersPriorityTextSelMode':        'CUDA',
'ProvidersPriorityTextSelModes':       ['CUDA', 'TensorRT', 'TensorRT-Engine', 'CPU'],
#
# Face Swapper Model
'FaceSwapperModelTextSelInfoText':  '换脸模型:\n选择换脸模型.',
'FaceSwapperModelTextSelMode':      'Inswapper128',
'FaceSwapperModelTextSelModes':     ['Inswapper128', 'SimSwap512', 'GhostFace-v1', 'GhostFace-v2', 'GhostFace-v3'],
#
'PreviewModeTextSelInfoText':       '',
'PreviewModeTextSelMode':           'Video',
'PreviewModeTextSelModes':          ['视频', '图像','全屏'],
'RecordTypeTextSelInfoText':        '视频录制库:\n选择用于视频录制的录制库。FFMPEG使用视频质量滑块来调整最终视频的大小和质量。OPENCV没有选择，但速度更快，效果良好.',
'RecordTypeTextSelMode':            'FFMPEG',
'RecordTypeTextSelModes':           ['FFMPEG', 'OPENCV'],
'RestorerDetTypeTextSelInfoText':   '检测对齐:\n原始：保留面部特征和表情，但可能会出现一些伪影。\n混合：更接近参考图像，但速度更快。\n参考：软化面部特征。',
'RestorerDetTypeTextSelMode':       '混合',
'RestorerDetTypeTextSelModes':      ['原始', '混合', '参考'],
'RestorerTypeTextSelInfoText':      '高清修复类型:\n选择 Restorer 类型.\n速度: GPEN 256>GFPGAN v1.4>CodeFormer>GPEN 512>GPEN 1024>GPEN 2048>VQFR v2',
'RestorerTypeTextSelMode':          'GFPGAN-v1.4',
'RestorerTypeTextSelModes':         ['GFPGAN-v1.4', 'CodeFormer', 'GPEN-256', 'GPEN-512', 'GPEN-1024', 'GPEN-2048', 'RestoreFormer++', 'VQFR-v2'],
'Restorer2DetTypeTextSelInfoText':   '检测对齐:\n原始：保留面部特征和表情，但可能会出现一些伪影。\n混合：更接近参考图像，但速度更快。\n参考：软化面部特征。',
'Restorer2DetTypeTextSelMode':       '混合',
'Restorer2DetTypeTextSelModes':      ['原始', '混合', '参考'],
'Restorer2TypeTextSelInfoText':      'RESTORER TYPE:\nSelect the Restorer type.\nSpeed: GPEN 256>GFPGAN v1.4>CodeFormer>GPEN 512>GPEN 1024>GPEN 2048>VQFR v2',
'Restorer2TypeTextSelMode':          'GFPGAN-v1.4',
'Restorer2TypeTextSelModes':         ['GFPGAN-v1.4', 'CodeFormer', 'GPEN-256', 'GPEN-512', 'GPEN-1024', 'GPEN-2048', 'RestoreFormer++', 'VQFR-v2'],

# Frame Enhancer
'FrameEnhancerTypeTextSelInfoText': '帧增强器类型:\n选择 Restorer 类型.\n速度: DeOldify Artistic>DeOldify Stable>DeOldify Video>DDColor Artistic>DDColor>RealEsrgan x2 plus>BSRGan x2>UltraMix x4>UltraSharp x4>RealEsrgan x4 plus>BSRGan x4',
'FrameEnhancerTypeTextSelMode':     'RealEsrgan-x2-Plus',
'FrameEnhancerTypeTextSelModes':    ['RealEsrgan-x2-Plus', 'RealEsrgan-x4-Plus', 'RealEsr-General-x4v3', 'BSRGan-x2', 'BSRGan-x4', 'UltraSharp-x4', 'UltraMix-x4', 'DDColor-Artistic', 'DDColor', 'DeOldify-Artistic', 'DeOldify-Stable', 'DeOldify-Video'],
#

# AutoColor
'AutoColorTypeTextSelInfoText':      '自动着色类型:\n选择方式.\n对于测试，汉斯方法有时会出现伪影',
'AutoColorTypeTextSelMode':          'Test',
'AutoColorTypeTextSelModes':         ['Test', 'Test_Mask', 'DFL_Test', 'DFL_Orig'],

# WebCam
'WebCamMaxResolSelInfoText':        "摄像头最大分辨率:\n选择摄像头最大分辨率",
'WebCamMaxResolSelMode':            '1920x1080',
'WebCamMaxResolSelModes':           ['480x360', '640x480', '1280x720', '1920x1080','2560x1440','3840x2160'],

'WebCamBackendSelInfoText':        "摄像头后端:\n根据你的操作系统选择摄像头的后端 ",
'WebCamBackendSelMode':            'Default',
'WebCamBackendSelModes':           ['Default', 'DirectShow','MSMF', 'V4L', 'V4L2','GSTREAMER'],

'WebCamMaxFPSSelInfoText':        "摄像头最大 FPS:\n选择摄像头最大FPS",
'WebCamMaxFPSSelMode':            30,
'WebCamMaxFPSSelModes':           [23,30,60],

'WebCamMaxNoSliderAmount':                 1,
'WebCamMaxNoSliderInc':                    1,
'WebCamMaxNoSliderInfoText':               '网络摄像头数量最大值:\n要检测到的网络摄像头的最大数量.',
'WebCamMaxNoSliderMax':                    10,
'WebCamMaxNoSliderMin':                    0,

'MergeTextSelInfoText':      '合并计算:\n当移动点击脸进行合并时，确定如何组合嵌入向量.',
'MergeTextSelMode':          'Mean',
'MergeTextSelModes':         ['Mean', 'Median'],
'SwapperTypeTextSelInfoText':      '像素输出分辨率:\n确定 像素输出的分辨率.',
'SwapperTypeTextSelMode':          '128',
'SwapperTypeTextSelModes':         ['128', '256', '512'],
'QualityModeTextSelInfoText':      '质量模式:\n快速: 最快速度，基础质量\n标准: 平衡速度和质量\n高质量: 更好效果，稍慢\n极致: 最佳质量，较慢',
'QualityModeTextSelMode':          '标准',
'QualityModeTextSelModes':         ['快速', '标准', '高质量', '极致'],
'UseEnhancedSwapSwitchInfoText':   '启用增强换脸:\n开启后使用增强算法\n提供更好的换脸质量',
'UseEnhancedSwapSwitchState':      True,

# Text Entry
'CLIPTextEntry':    '',
'CLIPTextEntryInfoText':            '输入文本遮罩:\n使用时，在方框中输入一个或多个单词，用逗号隔开，然后按 <enter> 键。.',

# Face Editor
'FaceEditorTypeTextSelInfoText':   '面部编辑器类型:\n在面编辑器中选择要编辑的目标类型.',
'FaceEditorTypeTextSelMode':       'Human-Face',
#'FaceEditorTypeTextSelModes':      ['Human-Face', 'Animal-Face'],
'FaceEditorTypeTextSelModes':      ['Human-Face'],
'FaceEditorType': '面部编辑器类型',

'FaceEditorIDSliderAmount':        1,
'FaceEditorIDSliderInc':           1,
'FaceEditorIDSliderInfoText':      '面部编辑器位置 面部 ID:\n确定帧中可以修改的目标脸部.',
'FaceEditorIDSliderMax':           20,
'FaceEditorIDSliderMin':           1,
'FaceEditorID': '脸部编辑器ID: ',

'CropScaleSliderAmount':           2.50,
'CropScaleSliderInc':              0.05,
'CropScaleSliderInfoText':         '裁剪范围:\n改变原脸（source）裁剪范围.',
'CropScaleSliderMax':              3.20,
'CropScaleSliderMin':              1.80,
'CropScale': '裁剪缩放',

'EyesOpenRatioSliderAmount':       0.00,
'EyesOpenRatioSliderInc':          0.01,
'EyesOpenRatioSliderInfoText':     '眼睛睁开大小:\n改变眼睛的睁开程度.',
'EyesOpenRatioSliderMax':          0.80,
'EyesOpenRatioSliderMin':          -0.80,
'EyesOpenRatio': '眼睛闭上 <--> 张开比例: ',

'LipsOpenRatioSliderAmount':       0.00,
'LipsOpenRatioSliderInc':          0.01,
'LipsOpenRatioSliderInfoText':     '嘴唇张开大小:\n改变嘴唇张开程度.',
'LipsOpenRatioSliderMax':          0.80,
'LipsOpenRatioSliderMin':          -0.80,
'LipsOpenRatio': '嘴唇闭上 <--> 张开比例: ',

'HeadPitchSliderAmount':           0,
'HeadPitchSliderInc':              1,
'HeadPitchSliderInfoText':         '头X轴俯仰角:\n改变头Pitch.',
'HeadPitchSliderMax':              15,
'HeadPitchSliderMin':              -15,
'HeadPitch': '头X轴俯仰角: ',

'HeadYawSliderAmount':             0,
'HeadYawSliderInc':                1,
'HeadYawSliderInfoText':           '头Y轴偏航角:\n改变头Yaw.',
'HeadYawSliderMax':                15,
'HeadYawSliderMin':                -15,
'HeadYaw': '头Y轴偏航角: ',

'HeadRollSliderAmount':            0,
'HeadRollSliderInc':               1,
'HeadRollSliderInfoText':          '头Z轴翻滚角:\n改变头Roll.',
'HeadRollSliderMax':               15,
'HeadRollSliderMin':               -15,
'HeadRoll': '头Z轴翻滚角: ',

'XAxisMovementSliderAmount':       0.00,
'XAxisMovementSliderInc':          0.01,
'XAxisMovementSliderInfoText':     'X轴移动:\n改变水平方向.',
'XAxisMovementSliderMax':          0.19,
'XAxisMovementSliderMin':          -0.19,
'XAxisMovement': 'X轴移动: ',

'YAxisMovementSliderAmount':       0.00,
'YAxisMovementSliderInc':          0.01,
'YAxisMovementSliderInfoText':     'Y轴移动:\n改变上下方向.',
'YAxisMovementSliderMax':          0.19,
'YAxisMovementSliderMin':          -0.19,
'YAxisMovement': 'Y轴移动: ',

'ZAxisMovementSliderAmount':       1.00,
'ZAxisMovementSliderInc':          0.01,
'ZAxisMovementSliderInfoText':     'Z轴移动:\n改变前后方向.',
'ZAxisMovementSliderMax':          1.20,
'ZAxisMovementSliderMin':          -0.90,
'ZAxisMovement': 'Z轴移动: ',

'MouthPoutingSliderAmount':        0.00,
'MouthPoutingSliderInc':           0.01,
'MouthPoutingSliderInfoText':      '噘嘴:\n噘起嘴巴.',
'MouthPoutingSliderMax':           0.09,
'MouthPoutingSliderMin':           -0.09,
'MouthPouting': '噘嘴: ',

'MouthPursingSliderAmount':        0.00,
'MouthPursingSliderInc':           0.01,
'MouthPursingSliderInfoText':      '缩嘴:\n缩拢嘴巴..',
'MouthPursingSliderMax':           15.00,
'MouthPursingSliderMin':           -20.00,
'MouthPursing': '缩嘴: ',

'MouthGrinSliderAmount':           0.00,
'MouthGrinSliderInc':              0.01,
'MouthGrinSliderInfoText':         '咧嘴:\n改变嘴巴咧开程度.',
'MouthGrinSliderMax':              15.00,
'MouthGrinSliderMin':              0.00,
'MouthGrin': '咧嘴: ',

'LipsCloseOpenSliderAmount':       0,
'LipsCloseOpenSliderInc':          1,
'LipsCloseOpenSliderInfoText':     '嘴唇闭合 <--> 打开大小 :\n改变嘴唇闭合或张开程度.',
'LipsCloseOpenSliderMax':          120,
'LipsCloseOpenSliderMin':          -90,
'LipsCloseOpen': '嘴唇闭合 <--> 打开大小: ',

'MouthSmileSliderAmount':          0.00,
'MouthSmileSliderInc':             0.01,
'MouthSmileSliderInfoText':        '嘴巴微笑:\n改变嘴巴微笑程度.',
'MouthSmileSliderMax':             1.30,
'MouthSmileSliderMin':             -0.30,
'MouthSmile': '嘴巴微笑: ',
'EyeWinkSliderAmount':             0.00,
'EyeWinkSliderInc':                0.01,
'EyeWinkSliderInfoText':           '眨眼:\nWinking eye.',
'EyeWinkSliderMax':                39.0,
'EyeWinkSliderMin':                0.0,
'EyeWink': '眨眼: ',

'EyeBrowsDirectionSliderAmount':   0.00,
'EyeBrowsDirectionSliderInc':      0.01,
'EyeBrowsDirectionSliderInfoText': '眉毛方向:\n改变眉毛方向.',
'EyeBrowsDirectionSliderMax':      30.00,
'EyeBrowsDirectionSliderMin':      -30.00,
'EyeBrowsDirection': '眉毛方向: ',


'EyeGazeHorizontalSliderAmount':   0.00,
'EyeGazeHorizontalSliderInc':      0.01,
'EyeGazeHorizontalSliderInfoText': '眼睛注视水平:\n改变眼睛水平注视方向.',
'EyeGazeHorizontalSliderMax':      30.00,
'EyeGazeHorizontalSliderMin':      -30.00,
'EyeGazeHorizontal': '眼睛注视水平: ',

'EyeGazeVerticalSliderAmount':     0.00,
'EyeGazeVerticalSliderInc':        0.01,
'EyeGazeVerticalSliderInfoText':   '眼睛凝视垂直:\n改变眼睛垂直注视方向.',
'EyeGazeVerticalSliderMax':        63.00,
'EyeGazeVerticalSliderMin':        -63.00,
'EyeGazeVertical': '眼睛凝视垂直: ',

# GUI Text
'WindowTitle': 'Rope-Live Stellar',
'StarPluckWindowTitle': 'StarPluck',
'TargetVideoTitle': '要换脸的视频',
'DesiredAvatarTitle': '目标头像',
'FoundFacesTitle': '找到的人脸',
'MergedFacesTitle': '合并的人脸',
'DFMModelTitle': '脸模型文件（DFM）',
'SettingsTitle': '设置',
'GetStreamKeyTitle': '获取推流码',
'OBSStreamTitle': 'OBS配置',
'CameraExtensionTitle': '摄像头扩展',
'LanguageSettingsTitle': '语言',
'NpcapErrorTitle': '错误',
'NpcapErrorMessage': 'Npcap 未安装。请访问 https://npcap.com 下载并安装 Npcap。',
'StreamSettingsSuccess': 'OBS推流设置成功，开始直播吧。',
'StreamSettingsWarning': 'OBS推流设置可能未成功更新，请手动检查OBS设置',
'StreamSettingsError': '设置OBS时出错：',
'NoStreamInfoError': '获取推流信息失败。请确保直播程序已打开。',
'LanguageChangeSuccess': '语言切换成功，程序将自动重启。',
'LanguageChangeError': '切换语言失败：',
'InputPanelTitle': '输入面板',
'FacesPanelTitle': '脸部面板',
'ParamsPanelTitle': '参数面板',
'KeyboardShortcutsTitle': '键盘快捷键',
'ParametersVisibilityTitle': '参数可视化',
'LivePortraitTitle': '编辑人脸设置',
'FaceSwapperTitle': '换脸参数设置',
'FaceEditorTitle': '编辑人脸',
'FaceSwapperParametersTitle': '换脸参数',
'ThreadsTitle': '线程数量',
'FaceSwapperModelTitle': '换脸模型',
'SwapperResolution': '换脸像素',
'QualityModeTitle': '质量模式',
'UseEnhancedSwapTitle': '启用增强换脸',
'WebcamBackendTitle': '摄像头后端',
'WebcamResolution': '摄像头分辨率',
'WebcamFPS': '摄像头每秒帧数',
'WebcamCount': '摄像头最大数量',
'SendFramesToVirtualCamera': '发送至虚拟摄像头',
'RestorerTitle': '高清修复',
'RestorerModelTitle': '修复模型',
'DetectionAlignment': '检测对齐',
'RestorerFidelity': '保真度比率',
'RestorerBlend': '混合度',
'Restorer2Title': '高清修复2',
'Restorer2ModelTitle': '修复模型',
'Restorer2DetectionAlignment': '检测对齐',
'Restorer2Blend': '混合度',
'EnhancerType': '帧类型',
'FrameEnhancerBlend': '混合度',
'Orientation': '方向',
'OrientationAngle': '角度',
'Strength': '强度',
'StrengthValue': '强度值',
'BorderTop': '边框顶部距离',
'BorderLeft': '边框左侧距离',
'BorderRight': '边框右侧距离',
'BorderBottom': '边框底部距离',
'BorderBlur': '边框模糊',
'Diff': '差异化',
'DiffValue': '差异值',
'DiffingBlur': '差异模糊',
'Occluder': '自动遮罩',
'OccluderSize': '大小',
'DFLXSegSliderSize': '大小',
'DFLMask': 'DFL 掩码',
'DFLXSeg': 'DFL 遮罩',
'OccluderBlur': '遮挡模糊',
'FinalBlurSwitch': '最终模糊开关',
'FinalBlurValue': '最终模糊值',
'OverallMaskBlend': '整体蒙版混合',
'DFLRCTColorTransfer': 'DFL RCT 颜色转移',
'DFLLoadOnlyOne': 'DFL 仅加载一个模型',
'DFLAmpMorphFactor': 'DFL AMP 变形因子',
'CLIP': 'CLIP',
'CLIPValue': '数',
'CLIPMask': '文字蒙版',
'CLIPText': '输入英文文本',
'RestoreEyes': '保留眼睛',
'RestoreEyesBlend': '眼睛混合',
'EyesMouthBlur': '眼睛嘴巴模糊',
'RestoreEyesFeather': '眼睛边缘羽化',
'RestoreEyesSize': '眼睛区域大小',
'RestoreEyesRadiusFactorX': '眼睛半径系数:X',
'RestoreEyesRadiusFactorY': '眼睛半径系数:Y',
'RestoreEyesSpacingOffset': '眼间距偏移',
'RestoreEyesXoffset': '眼睛抵消:X',
'RestoreEyesYoffset': '眼睛抵消:Y',
'RestoreMouth': '保留嘴巴',
'RestoreMouthBlend': '嘴巴混合',
'RestoreMouthFeather': '嘴巴边缘羽化',
'RestoreMouthSize': '嘴巴大小',
'RestoreMouthRadiusFactorX': '嘴巴半径系数:X',
'RestoreMouthRadiusFactorY': '嘴巴半径系数:Y',
'RestoreMouthXoffset': '嘴巴抵消:X',
'RestoreMouthYoffset': '嘴巴抵消:Y',
'BGParserBlur': '背景模糊',
'ParserBlur': '有脸解析模糊',
'FaceParserGB': '背景',
'NeckParser': '颈部',
'LeftEyeBrowParser': '左眉毛',
'RightEyeBrowParser': '右眉毛',
'LeftEyeParser': '左眼睛',
'RightEyeParser': '右眼睛',
'NoseParser': '鼻子',
'MouthParser': '嘴巴',
'LipsParser': '嘴唇',
'UpperLipParser': '上嘴唇',
'LowerLipParser': '下嘴唇',
'FaceParser': '面部',
'AutoColor': '自动着色',
'AutoColorBlend': '自动着色混合',
'SimilarityType': '相似度类型',
'JpegCompression': 'JPEG压缩',
'JpegCompressionValue': 'JPEG压缩值',
'ColorAdjustment': '色彩调整',
'ColorRed': '红色',
'ColorGreen': '绿色',
'ColorBlue': '蓝色',
'ColorBright': '亮度',
'ColorContrast': '对比度',
'ColorSaturation': '饱和度',
'ColorSharpness': '锐度',
'ColorHue': '色调',
'ColorGamma': '伽马',
'Noise': '噪点',
'FaceAdj': '输入面部调整',
'KPSX': 'KPS X',
'KPSY': 'KPS Y',
'KPSScale': 'KPS 缩放',
'FaceScale': '人脸缩放',
'FaceLikeness': '人脸相似度',
'FaceLikenessFactor': '人脸相似度因子',
'Threshold': '阈值',
'DetectType': '检测类型',
'DetectScore': '检测得分',
'AutoRotation': '自动旋转',
'LandmarksDetectionAdj': '地标检测调整',
'LandmarksAlignModeFromPoints': '起点',
'LandmarksDetectType': '地标检测类型',
'LandmarksDetectScore': '地标检测分数',
'ShowLandmarks': '显示地标',
'LandmarksPositionAdj': '地标位置调整',
'FaceID': '人脸ID',
'EyeLeftX': '左眼X',
'EyeLeftY': '左眼Y',
'EyeRightX': '右眼X',
'EyeRightY': '右眼Y',
'NoseX': '鼻子X',
'NoseY': '鼻子Y',
'MouthLeftX': '嘴巴左X',
'MouthLeftY': '嘴巴左Y',
'MouthRightX': '嘴巴右X',
'MouthRightY': '嘴巴右Y',
'RecordType': '录制类型',
'VideoQual': '视频质量',
'AudioSpeed': '音频播放速度',
'Merge': '合并计算',
'HelpTextSel': '帮助文档',
'QQText': 'QQ：**********',
'TelegramText': '电报 @tttt87877',
'LicenseStatusText': '状态：{0}',
'TrialText': '试用中',
'TrialDays': '试用剩余 {0} 天',
'Remaining': '剩余 {0} 天',
'Warning': '提示',
'LanguageSwitched': '语言更换成功，点击确定重启生效。',
'OK': '确定',
'Error': '错误',
'RestartText': '点击确定，重新启动。',
'LanguageChangeError': '切换语言失败：',
'GetStreamKeyError': '获取推流信息时发生错误：',
'OBSStreamSuccess': 'OBS推流设置成功，开始直播吧。',
'OBSStreamWarning': 'OBS推流设置可能未成功更新，请手动检查OBS设置',
'CurrentStreamSettings': '当前的流服务设置信息：',
'ActivationTitle': '需要激活',  
'ActivationSuccess': '激活成功',  # 英文版为 'Success'
'ActivationSuccessMsg': '有效期: {0}',  # 使用占位符
'ActivationError': '错误',  # 英文版为 'Error'
'InvalidActivationCode': '激活码无效或已过期',  # 英文版为 'Invalid or expired activation code'
'ActivationCodeLabel': '输入激活码:',  # 英文版为 'Activation Code:'
'ActivateButton': '激活',  # 英文版为 'Activate'
'BuyActivationCodeLabel': '试用期结束，请购买激活码。\n激活时，请保持网络畅通。', 
'BuyActivationCode': '购买', 
'BuyActivationCodeLink': 'https://stellar.ai-yy.com/#pricing',
'TelegramUrl': 'https://t.me/tttt87877',
'GetMachineCodeError': '获取机器码时出错: {0}',  # 英文版为 'Error getting machine code: {0}'
'CreateActivationWindowError': '创建激活窗口时出错: {0}',  # 英文版为 'Error creating activation window: {0}'
'StartupMessage': '开始你的表演吧！',  # 英文版为 'Software activated. Let the show begin!'
'ProcessingCoreTitle': '计算内核',
'UpdateNDIWebcamConfigError': '更新 NDI Webcam 配置失败: {0}',  
'UpdateOBSWebSocketConfigError': '更新 OBS WebSocket 配置失败: {0}',  
'ExtendCameras': '正在扩展摄像头，请稍候...',
'ExtendCamerasSuccess': '扩展摄像头成功！',
'Success': '成功',
'ExtendCamerasError': '扩展摄像头失败。',
'CameraExtensionPathInvalid': '扩展摄像头路径无效',
'InvalidAppPath': '无效的应用程序路径: {0}',
'TerminateProcessError': '终止 {0} 进程失败: {1}',
'StartApplicationError': '启动 {0} 失败: {1}',
'AccessDenied': '无法访问 {0} 进程信息: {1}',
'OBSPathNotFound': '未找到 OBS 安装路径',
'OBSWebSocketPluginConfigNotFound': 'OBS WebSocket 插件配置文件不存在',
'OBSNotReady': 'OBS 未能完全启动，请检查 OBS 是否正常运行',
'ActivationFailed': '软件未激活或许可证无效。',
'ImageResourceLoadFailed': '图像资源加载失败，请检查程序完整性',
'ErrorRunningCoordinator': '运行程序时出错: {0}',
'TkinterError': 'Tkinter 错误: {0}',
'MachineCode': '机器码',
'Permanent': '永久有效',
'1day': '1天',
'3days': '3天',
'1week': '1周',
'1month': '1个月',
'3months': '3个月',
'6months': '6个月',
'1year': '1年',
'Save': '保存',
'Apply': '应用',
'Load': '加载',
'Default': '缺省',
'RTMPSnifferInfoText': '请开启直播，准备捕获数据包...',
'ConfigFileNotFound': '配置文件不存在',
'ConfigFileFormatError': '配置文件格式不正确',
'StartRTMPSniffer': '启动 RTMP 嗅探器...',
'UsedInterface': '使用的网络接口',
'UsedDisplayFilter': '使用的显示过滤器',
'UsedTsharkPath': '使用的 tshark 路径',
'TimeConsumed': '耗时',
'Seconds': '秒',
'SuccessRTMPInfo': '成功获取 RTMP 信息:',
'OnlyServerInfo': '仅获取到服务器信息',
'NoStreamKeyInfo': '未能获取到推流码信息',
'NoRTMPInfo': '未能获取到 RTMP 信息。请确保直播应用已打开并尝试开始直播。',
'Packets': '数据包',
'Found': '找到',
'Server': '服务器',
'StreamKey': '推流码',
'RTMPInfo': 'RTMP 信息',
'LaunchError': '启动错误',
'LaunchErrorMsg': '请使用启动器运行程序',
'LoadLicenseInfoError': '加载许可证信息失败: {0}',
'SaveLicenseInfoError': '保存许可证信息失败: {0}',
'NoLicenseInfo': '未找到许可证信息',
'IncompleteLicenseInfo': '许可证信息不完整',
'LicenseMachineIDMismatch': '许可证与当前机器不匹配',
'LicenseExpired': '许可证已过期',
'LicenseVerificationFailed': '许可证验证失败',
'LicenseVerificationFailedMsg': '在线验证失败，使用本地验证',
'LicenseSignatureInvalid': '许可证签名无效',
'LicenseValid': '许可证有效',   
'CheckLicenseError': '检查许可证时出错: {0}',
'LaunchTimeout': '启动超时',
'LicenseInvalid': '激活码无效',
'LicenseActivated': '激活成功',
'RegisterSoftware': '注册软件',
'EmptyActivationCode': '请输入激活码',
'LicenseExpired': '许可证已过期',
'LicenseUsedOnOtherMachine': '此激活码已在其他设备上使用',
'LicenseActivationError': '激活失败，许可证无效',
'JoinGroup': '加入群聊',
'GroupUrl': 'https://t.me/+n7Mda_NATtBlMmJh',
'ExpiredOn': '已于 {} 过期',
'ExpiringToday': '{} 到期',
'ExpiresOn': '{} 到期',
'LicenseVersionMismatch': '此激活码不适用于当前版本',
'HelpUrl': 'https://docs.ai-yy.com',
}

PARAM_VARS =    {

    'CLIPState':                False,
    'CLIPMode':                 0,
    'CLIPModes':                ['CLIP'],
    'CLIPAmount':               [50],
    'CLIPMin':                  0,
    'CLIPMax':                  100,
    'CLIPInc':                  1,
    'CLIPUnit':                 '%',
    'CLIPIcon':                 get_media_path('CLIP.png'),
    'CLIPMessage':              'CLIP - 基于文本的遮挡器. 被遮挡的对象在最终图像中可见（从蒙版中被遮挡）. [LB: on/off, MW: strength]',
    'CLIPFunction':         False,

    "CLIPText":                 '',
}

PARAMS =   {

    'ClearmemFunction':         'self.clear_mem()',
    'PerfTestFunction':         'self.toggle_perf_test()',
    'ImgVidFunction':         'self.toggle_vid_img()',
    'AutoSwapFunction':         'self.toggle_auto_swap()',
    'SaveImageFunction':         'self.save_image()',

    'ClearmemIcon':            get_media_path('clear_mem.png'),
    'SaveImageIcon':            get_media_path('save_disk.png'),
    'PerfTestIcon':            get_media_path('test.png'),
    'RefDelIcon':          get_media_path('construction.png'),
    'TransformIcon':          get_media_path('scale.png'),
    'ThresholdIcon':            get_media_path('thresh.png'),
    'LoadSFacesIcon':            get_media_path('save.png'),
    'BorderIcon':                 get_media_path('maskup.png'),
    'OccluderIcon':             get_media_path('occluder.png'),
    'ColorIcon':            get_media_path('rgb.png'),
    'StrengthIcon':             get_media_path('strength.png'),
    'OrientationIcon':          get_media_path('orient.png'),
    'DiffIcon':                 get_media_path('diff.png'),
    'MouthParserIcon':           get_media_path('parse.png'),
    'AudioIcon':            get_media_path('rgb.png'),
    'VideoQualityIcon':            get_media_path('tarface.png'),
    'MaskViewIcon':             get_media_path('maskblur.png'),
    'CompareViewIcon':          get_media_path('maskblur.png'),
    'BlurIcon':                 get_media_path('blur.png'),
    'ToggleStopIcon':            get_media_path('STOP.png'),
     'DelEmbedIcon':            get_media_path('delemb.png'),
    'ImgVidIcon':            get_media_path('imgvid.png'),

    'ImgVidMessage':         '图片/视频 - 在图像和视频文件夹视图之间切换。',
    'ToggleStopMessage':         '停止标记 - 设置停止视频播放/录制的帧.',
    'AutoSwapMessage':         '自动替换 - 将图像中的第一个人自动替换为选定的源面孔s [LB: Turn on/off]',
    'SaveImageMessage':         '保存图片 - 保存图片到输出文件夹',
    'ClearmemMessage':         '清显存 - 卸载显卡上的模型 [LB: Clear]',
    'PerfTestMessage':         '性能数据 - 在控制台中显示关键StarPluck功能的计时数据. [LB: on/off]',
    'RefDelMessage':       '参考点 - 修改参考点。打开遮罩预览以查看调整。 [LB: on/off, RB: translate x/y, and scale, MW: amount]' ,
    'ThresholdMessage':         '阈值 - 确定目标面是否与帧中的面匹配的阈值。越低越严格. [LB: use amount/match all, MW: value]',
    'TransformMessage':       '范围 - 调整脸部的比例。与背景解析器一起使用以融入图像. [LB: on/off, MW: amount]',
    'PlayMessage':         '播放 - 播放视频。再次按停止播放',
    'SaveHotkey':         '保存快捷键',
    'TimeLineStart':         '时间轴开始',
    'TimeLineLeft30':         '左移30帧',
    'TimeLineRight30':         '右移30帧',
    'Record':         '记录',
    'Play':         '播放',
    'SaveImage':         '保存图像',
    'AddMark':         '添加标记',
    'DelMark':         '删除标记',
    'PrevMark':         '上一个标记',
    'NextMark':         '下一个标记',
    'ToggleRestore':         '切换恢复',
    'ToggleRestore2':         '切换恢复2',
    'ToggleDirection':         '切换方向',
    'ToggleStrength':         '切换强度',
    'ToggleDiff':         '切换差异',
    'ToggleOccluder':         '切换遮挡',
    'ToggleFaceParser':         '切换人脸解析',
    'ToggleCLIP':         '切换基于文本的蒙版',
    'ToggleColorAdjustment':         '切换颜色调整',
    'ToggleFaceAdjustment':         '切换面部调整',
    'ClearMem':         '清除显存',
    'SwapFaces':         '换脸',
    'TimeLineLeft1':         '左移1帧',
    'TimeLineRight1':         '右移1帧',
    'MaskView':         '显示蒙版',
     }

CAMERA_BACKENDS = {
    'Default': cv2.CAP_ANY,
    'DirectShow': cv2.CAP_DSHOW,
    'MSMF': cv2.CAP_MSMF,
    'V4L': cv2.CAP_V4L,
    'V4L2': cv2.CAP_V4L2,
    'GSTREAMER': cv2.CAP_GSTREAMER,
}