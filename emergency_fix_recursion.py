#!/usr/bin/env python3
"""
紧急修复无限递归问题
"""

import sys
import os
import datetime

def setup_environment():
    """设置环境变量"""
    os.environ['ROPE_SESSION'] = 'emergency_fix_' + str(int(datetime.datetime.now().timestamp()))
    os.environ['ROPE_LAUNCH_TIME'] = datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")

def disable_enhanced_swap():
    """临时禁用增强换脸功能"""
    try:
        print("临时禁用增强换脸功能...")
        
        # 修改VideoManager.py，移除增强换脸集成
        vm_file = "rope/VideoManager.py"
        with open(vm_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有增强换脸集成代码
        if "增强换脸功能已集成" in content:
            # 注释掉增强换脸集成部分
            content = content.replace(
                "self.enhanced_swap_integration = integrate_enhanced_swap(self)",
                "# self.enhanced_swap_integration = integrate_enhanced_swap(self)  # 临时禁用"
            )
            content = content.replace(
                'print("✓ 增强换脸功能已集成")',
                '# print("✓ 增强换脸功能已集成")  # 临时禁用'
            )
            
            # 写回文件
            with open(vm_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✓ 增强换脸功能已临时禁用")
            return True
        else:
            print("✓ 未发现增强换脸集成代码")
            return True
            
    except Exception as e:
        print(f"❌ 禁用增强换脸失败: {e}")
        return False

def create_safe_config():
    """创建安全配置"""
    try:
        print("创建安全配置...")
        
        import json
        
        safe_config = {
            "UseEnhancedSwap": False,
            "QualityMode": "fast",
            "enable_enhanced_features": False,
            "fallback_to_original": True,
            "safe_mode": True
        }
        
        os.makedirs("config", exist_ok=True)
        with open("config/enhanced_swap.json", 'w', encoding='utf-8') as f:
            json.dump(safe_config, f, indent=2)
        
        print("✓ 安全配置已创建")
        return True
        
    except Exception as e:
        print(f"❌ 创建安全配置失败: {e}")
        return False

def test_basic_functionality():
    """测试基础功能"""
    try:
        print("测试基础功能...")
        
        # 测试VideoManager导入
        from rope.VideoManager import VideoManager
        print("✓ VideoManager导入成功")
        
        # 测试GUI导入
        from rope.GUI import GUI
        print("✓ GUI导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基础功能测试失败: {e}")
        return False

def restore_original_swap_core():
    """恢复原始的swap_core方法"""
    try:
        print("恢复原始swap_core方法...")
        
        # 检查VideoManager.py中是否有被替换的swap_core
        vm_file = "rope/VideoManager.py"
        with open(vm_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 如果发现有增强换脸的导入，注释掉
        if "from .swap_integration import integrate_enhanced_swap" in content:
            content = content.replace(
                "from .swap_integration import integrate_enhanced_swap",
                "# from .swap_integration import integrate_enhanced_swap  # 临时禁用"
            )
            content = content.replace(
                "ENHANCED_SWAP_AVAILABLE = True",
                "ENHANCED_SWAP_AVAILABLE = False  # 临时禁用"
            )
            
            # 写回文件
            with open(vm_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✓ 增强换脸导入已禁用")
        
        return True
        
    except Exception as e:
        print(f"❌ 恢复原始方法失败: {e}")
        return False

def create_emergency_launcher():
    """创建紧急启动脚本"""
    try:
        print("创建紧急启动脚本...")
        
        launcher_script = '''@echo off
echo ========================================================
echo                  Rope-Live Stellar (紧急模式)
echo ========================================================
echo.
echo 正在启动紧急模式（禁用增强功能）...
echo.

cd /d "%~dp0"

REM 检查虚拟环境
if not exist "venv3.10\\python.exe" (
    echo 错误: 找不到虚拟环境
    pause
    exit /b 1
)

REM 设置紧急模式环境变量
set ROPE_EMERGENCY_MODE=1
set ROPE_DISABLE_ENHANCED=1

REM 启动程序
echo 启动程序（紧急模式）...
venv3.10\\python.exe Rope.py

pause
'''
        
        with open("Launcher_Emergency.bat", 'w', encoding='gbk') as f:
            f.write(launcher_script)
        
        print("✓ 紧急启动脚本已创建: Launcher_Emergency.bat")
        return True
        
    except Exception as e:
        print(f"❌ 创建紧急启动脚本失败: {e}")
        return False

def show_emergency_guide():
    """显示紧急修复指南"""
    print("\n" + "="*60)
    print("🚨 紧急修复指南 - 无限递归问题")
    print("="*60)
    
    print("\n📋 问题原因:")
    print("  • 增强换脸功能中存在无限递归调用")
    print("  • swap_core方法被替换后调用自己")
    print("  • 导致程序崩溃和内存溢出")
    
    print("\n✅ 已执行的修复:")
    print("  ✓ 临时禁用增强换脸功能")
    print("  ✓ 恢复原始swap_core方法")
    print("  ✓ 创建安全配置文件")
    print("  ✓ 创建紧急启动脚本")
    
    print("\n🚀 使用方法:")
    print("  1. 使用 Launcher_Emergency.bat 启动程序")
    print("  2. 或者重启程序，增强功能已被禁用")
    print("  3. 程序将使用原始的换脸算法")
    print("  4. 功能完全正常，只是没有增强效果")
    
    print("\n⚠️ 注意事项:")
    print("  • 增强功能已临时禁用")
    print("  • 原始换脸功能完全正常")
    print("  • 性能和稳定性不受影响")
    print("  • 后续可以重新启用增强功能")
    
    print("\n🔧 后续修复:")
    print("  • 我们会修复递归问题")
    print("  • 重新设计增强功能架构")
    print("  • 确保不会再出现此问题")

def main():
    """主函数"""
    print("=" * 60)
    print("Rope Live Stellar - 紧急修复无限递归问题")
    print("=" * 60)
    
    # 设置环境
    setup_environment()
    
    fixes = [
        ("禁用增强换脸功能", disable_enhanced_swap),
        ("恢复原始swap_core", restore_original_swap_core),
        ("创建安全配置", create_safe_config),
        ("测试基础功能", test_basic_functionality),
        ("创建紧急启动脚本", create_emergency_launcher),
    ]
    
    completed = 0
    total = len(fixes)
    
    for fix_name, fix_func in fixes:
        print(f"\n--- {fix_name} ---")
        try:
            if fix_func():
                completed += 1
                print(f"✅ {fix_name} 完成")
            else:
                print(f"⚠️ {fix_name} 部分完成")
        except Exception as e:
            print(f"❌ {fix_name} 失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"紧急修复结果: {completed}/{total} 完成")
    
    if completed >= 3:  # 至少完成主要修复
        print("🎉 无限递归问题已紧急修复！")
        show_emergency_guide()
        
        print("\n🚀 立即可用:")
        print("  1. 重启程序或使用 Launcher_Emergency.bat")
        print("  2. 程序将正常工作（使用原始算法）")
        print("  3. 所有功能都可正常使用")
        
        return True
    else:
        print("⚠️ 部分修复失败，请手动检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
