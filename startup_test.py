
import sys
import os

# 设置环境
os.environ['ROPE_SESSION'] = 'startup_test'
os.environ['ROPE_LAUNCH_TIME'] = '2025/06/12 15:15:00'

try:
    # 测试导入
    from rope.VideoManager import VideoManager
    print("✓ VideoManager导入成功")
    
    from rope.safe_swap_integration import SafeSwapIntegration
    print("✓ SafeSwapIntegration导入成功")
    
    from rope.enhanced_face_swap_v2 import EnhancedFaceSwapperV2
    print("✓ EnhancedFaceSwapperV2导入成功")
    
    print("✓ 所有关键模块导入成功，系统应该能正常启动")
    
except Exception as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)
