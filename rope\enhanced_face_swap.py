"""
增强的换脸处理模块
提供更高质量的换脸效果和优化算法
"""

import torch
import torch.nn.functional as F
import cv2
import numpy as np
from typing import Tuple, Optional, Dict, Any
import time
from dataclasses import dataclass

from .logger import get_logger, LogExecutionTime
from .resource_manager import resource_manager

@dataclass
class EnhancedSwapConfig:
    """增强换脸配置"""
    # 质量设置
    target_resolution: int = 512  # 目标分辨率
    upscale_factor: float = 2.0   # 上采样倍数
    enable_super_resolution: bool = True  # 启用超分辨率
    
    # 混合算法
    blend_method: str = "poisson"  # poisson, alpha, seamless
    blend_strength: float = 0.8    # 混合强度
    feather_amount: int = 15       # 羽化程度
    
    # 色彩匹配
    enable_color_matching: bool = True
    color_transfer_method: str = "histogram"  # histogram, lab, reinhard
    
    # 后处理
    enable_face_enhancement: bool = True
    enable_skin_smoothing: bool = True
    skin_smooth_strength: float = 0.3
    
    # 性能优化
    use_half_precision: bool = True
    enable_tensorrt: bool = True
    batch_processing: bool = True

class EnhancedFaceSwapper:
    """增强的换脸处理器"""
    
    def __init__(self, config: EnhancedSwapConfig = None):
        self.config = config or EnhancedSwapConfig()
        self.logger = get_logger("EnhancedFaceSwapper")
        
        # 缓存常用的变换矩阵
        self._transform_cache = {}
        self._color_transfer_cache = {}
        
        # 预计算的滤波器
        self._gaussian_kernels = {}
        self._laplacian_kernels = {}
        
        self._init_kernels()
    
    def _init_kernels(self):
        """初始化常用的卷积核"""
        # 高斯模糊核
        for sigma in [1.0, 2.0, 3.0]:
            kernel_size = int(6 * sigma + 1)
            if kernel_size % 2 == 0:
                kernel_size += 1
            
            kernel = cv2.getGaussianKernel(kernel_size, sigma)
            kernel = torch.from_numpy(kernel @ kernel.T).float()
            self._gaussian_kernels[sigma] = kernel.unsqueeze(0).unsqueeze(0)
        
        # 拉普拉斯锐化核
        laplacian = torch.tensor([
            [0, -1, 0],
            [-1, 5, -1],
            [0, -1, 0]
        ], dtype=torch.float32).unsqueeze(0).unsqueeze(0)
        self._laplacian_kernels['sharpen'] = laplacian
    
    def enhanced_swap_core(self, img: torch.Tensor, kps_5: np.ndarray, 
                          source_embedding: np.ndarray, target_embedding: np.ndarray,
                          swapper_model, parameters: Dict[str, Any]) -> torch.Tensor:
        """
        增强的换脸核心算法
        
        Args:
            img: 输入图像 (C, H, W)
            kps_5: 5点人脸关键点
            source_embedding: 源人脸特征
            target_embedding: 目标人脸特征
            swapper_model: 换脸模型
            parameters: 参数字典
            
        Returns:
            处理后的图像
        """
        with LogExecutionTime("enhanced_face_swap"):
            with resource_manager.gpu_memory_context():
                # 1. 预处理优化
                img_preprocessed = self._advanced_preprocess(img, kps_5)
                
                # 2. 高质量人脸对齐
                aligned_face, transform_matrix = self._high_quality_alignment(
                    img_preprocessed, kps_5
                )
                
                # 3. 多尺度换脸处理
                swapped_face = self._multi_scale_swap(
                    aligned_face, source_embedding, swapper_model
                )
                
                # 4. 高级后处理
                enhanced_face = self._advanced_postprocess(
                    swapped_face, aligned_face, parameters
                )
                
                # 5. 智能混合
                result = self._intelligent_blending(
                    img, enhanced_face, transform_matrix, kps_5, parameters
                )
                
                return result
    
    def _advanced_preprocess(self, img: torch.Tensor, kps_5: np.ndarray) -> torch.Tensor:
        """高级预处理"""
        # 1. 自适应直方图均衡化
        img_enhanced = self._adaptive_histogram_equalization(img)
        
        # 2. 噪声抑制
        img_denoised = self._bilateral_filter(img_enhanced)
        
        # 3. 锐化增强
        if self.config.enable_face_enhancement:
            img_sharpened = self._unsharp_masking(img_denoised)
            return img_sharpened
        
        return img_denoised
    
    def _adaptive_histogram_equalization(self, img: torch.Tensor) -> torch.Tensor:
        """自适应直方图均衡化"""
        # 转换到LAB色彩空间进行处理
        img_np = img.permute(1, 2, 0).cpu().numpy()
        img_np = (img_np * 255).astype(np.uint8)
        
        # 转换到LAB
        lab = cv2.cvtColor(img_np, cv2.COLOR_RGB2LAB)
        
        # 对L通道进行CLAHE
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        lab[:, :, 0] = clahe.apply(lab[:, :, 0])
        
        # 转换回RGB
        enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)
        enhanced = torch.from_numpy(enhanced).float() / 255.0
        
        return enhanced.permute(2, 0, 1).to(img.device)
    
    def _bilateral_filter(self, img: torch.Tensor, d: int = 9, 
                         sigma_color: float = 75, sigma_space: float = 75) -> torch.Tensor:
        """双边滤波降噪"""
        img_np = img.permute(1, 2, 0).cpu().numpy()
        img_np = (img_np * 255).astype(np.uint8)
        
        filtered = cv2.bilateralFilter(img_np, d, sigma_color, sigma_space)
        filtered = torch.from_numpy(filtered).float() / 255.0
        
        return filtered.permute(2, 0, 1).to(img.device)
    
    def _unsharp_masking(self, img: torch.Tensor, amount: float = 1.5, 
                        radius: float = 1.0, threshold: float = 0.0) -> torch.Tensor:
        """USM锐化"""
        # 高斯模糊
        blurred = self._apply_gaussian_blur(img, radius)
        
        # 计算锐化掩码
        mask = img - blurred
        
        # 应用阈值
        if threshold > 0:
            mask = torch.where(torch.abs(mask) < threshold, 
                             torch.zeros_like(mask), mask)
        
        # 应用锐化
        sharpened = img + amount * mask
        return torch.clamp(sharpened, 0, 1)
    
    def _apply_gaussian_blur(self, img: torch.Tensor, sigma: float) -> torch.Tensor:
        """应用高斯模糊"""
        if sigma not in self._gaussian_kernels:
            # 动态创建核
            kernel_size = int(6 * sigma + 1)
            if kernel_size % 2 == 0:
                kernel_size += 1
            
            kernel = cv2.getGaussianKernel(kernel_size, sigma)
            kernel = torch.from_numpy(kernel @ kernel.T).float()
            self._gaussian_kernels[sigma] = kernel.unsqueeze(0).unsqueeze(0)
        
        kernel = self._gaussian_kernels[sigma].to(img.device)
        
        # 对每个通道应用卷积
        channels = []
        for i in range(img.shape[0]):
            channel = img[i:i+1].unsqueeze(0)
            blurred = F.conv2d(channel, kernel, padding=kernel.shape[-1]//2)
            channels.append(blurred.squeeze(0))
        
        return torch.cat(channels, dim=0)
    
    def _high_quality_alignment(self, img: torch.Tensor, kps_5: np.ndarray) -> Tuple[torch.Tensor, np.ndarray]:
        """高质量人脸对齐"""
        # 使用更精确的对齐算法
        target_size = self.config.target_resolution
        
        # 计算最优变换矩阵
        dst_points = np.array([
            [38.2946, 51.6963],
            [73.5318, 51.5014], 
            [56.0252, 71.7366],
            [41.5493, 92.3655],
            [70.7299, 92.2041]
        ]) * (target_size / 112.0)
        
        # 使用最小二乘法计算变换
        transform_matrix = cv2.estimateAffinePartial2D(
            kps_5.astype(np.float32), 
            dst_points.astype(np.float32),
            method=cv2.LMEDS
        )[0]
        
        # 应用变换
        img_np = img.permute(1, 2, 0).cpu().numpy()
        aligned = cv2.warpAffine(
            img_np, transform_matrix, 
            (target_size, target_size),
            flags=cv2.INTER_LANCZOS4,
            borderMode=cv2.BORDER_REFLECT
        )
        
        aligned_tensor = torch.from_numpy(aligned).permute(2, 0, 1).to(img.device)
        
        return aligned_tensor, transform_matrix
    
    def _multi_scale_swap(self, aligned_face: torch.Tensor, 
                         source_embedding: np.ndarray, swapper_model) -> torch.Tensor:
        """多尺度换脸处理"""
        if not self.config.enable_super_resolution:
            # 标准处理
            return self._standard_swap(aligned_face, source_embedding, swapper_model)
        
        # 多尺度处理
        scales = [1.0, 1.5, 2.0] if self.config.upscale_factor >= 2.0 else [1.0, 1.5]
        results = []
        
        for scale in scales:
            if scale != 1.0:
                # 上采样
                h, w = aligned_face.shape[1], aligned_face.shape[2]
                new_h, new_w = int(h * scale), int(w * scale)
                scaled_face = F.interpolate(
                    aligned_face.unsqueeze(0), 
                    size=(new_h, new_w), 
                    mode='bicubic', 
                    align_corners=False
                ).squeeze(0)
            else:
                scaled_face = aligned_face
            
            # 换脸处理
            swapped = self._standard_swap(scaled_face, source_embedding, swapper_model)
            
            # 下采样回原尺寸
            if scale != 1.0:
                swapped = F.interpolate(
                    swapped.unsqueeze(0),
                    size=(aligned_face.shape[1], aligned_face.shape[2]),
                    mode='bicubic',
                    align_corners=False
                ).squeeze(0)
            
            results.append(swapped)
        
        # 融合多尺度结果
        return self._fuse_multi_scale_results(results)
    
    def _standard_swap(self, face: torch.Tensor, source_embedding: np.ndarray, 
                      swapper_model) -> torch.Tensor:
        """标准换脸处理"""
        # 这里调用原有的换脸模型
        # 具体实现需要根据现有的swapper_model接口
        
        # 转换为模型期望的格式
        face_input = face.unsqueeze(0)  # 添加batch维度
        
        # 调用换脸模型（这里需要适配现有接口）
        with torch.no_grad():
            # 假设这是调用现有模型的方式
            swapped = swapper_model(face_input, source_embedding)
        
        return swapped.squeeze(0)
    
    def _fuse_multi_scale_results(self, results: list) -> torch.Tensor:
        """融合多尺度结果"""
        if len(results) == 1:
            return results[0]
        
        # 加权平均融合
        weights = [0.4, 0.35, 0.25][:len(results)]
        fused = torch.zeros_like(results[0])
        
        for result, weight in zip(results, weights):
            fused += weight * result
        
        return fused

    def _advanced_postprocess(self, swapped_face: torch.Tensor,
                            original_face: torch.Tensor,
                            parameters: Dict[str, Any]) -> torch.Tensor:
        """高级后处理"""
        result = swapped_face

        # 1. 色彩匹配
        if self.config.enable_color_matching:
            result = self._color_transfer(result, original_face)

        # 2. 皮肤平滑
        if self.config.enable_skin_smoothing:
            result = self._skin_smoothing(result, self.config.skin_smooth_strength)

        # 3. 细节增强
        result = self._detail_enhancement(result)

        # 4. 边缘优化
        result = self._edge_optimization(result, original_face)

        return result

    def _color_transfer(self, source: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """色彩迁移"""
        if self.config.color_transfer_method == "histogram":
            return self._histogram_matching(source, target)
        elif self.config.color_transfer_method == "lab":
            return self._lab_color_transfer(source, target)
        else:
            return self._reinhard_color_transfer(source, target)

    def _histogram_matching(self, source: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """直方图匹配"""
        source_np = (source.permute(1, 2, 0).cpu().numpy() * 255).astype(np.uint8)
        target_np = (target.permute(1, 2, 0).cpu().numpy() * 255).astype(np.uint8)

        matched = np.zeros_like(source_np)

        for i in range(3):  # RGB三个通道
            source_hist, _ = np.histogram(source_np[:, :, i], bins=256, range=(0, 256))
            target_hist, _ = np.histogram(target_np[:, :, i], bins=256, range=(0, 256))

            # 计算累积分布函数
            source_cdf = source_hist.cumsum()
            target_cdf = target_hist.cumsum()

            # 归一化
            source_cdf = source_cdf / source_cdf[-1]
            target_cdf = target_cdf / target_cdf[-1]

            # 创建映射表
            mapping = np.zeros(256, dtype=np.uint8)
            for j in range(256):
                idx = np.argmin(np.abs(target_cdf - source_cdf[j]))
                mapping[j] = idx

            matched[:, :, i] = mapping[source_np[:, :, i]]

        matched_tensor = torch.from_numpy(matched).float() / 255.0
        return matched_tensor.permute(2, 0, 1).to(source.device)

    def _lab_color_transfer(self, source: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """LAB色彩空间色彩迁移"""
        source_np = (source.permute(1, 2, 0).cpu().numpy() * 255).astype(np.uint8)
        target_np = (target.permute(1, 2, 0).cpu().numpy() * 255).astype(np.uint8)

        # 转换到LAB空间
        source_lab = cv2.cvtColor(source_np, cv2.COLOR_RGB2LAB).astype(np.float32)
        target_lab = cv2.cvtColor(target_np, cv2.COLOR_RGB2LAB).astype(np.float32)

        # 计算统计量
        source_mean = np.mean(source_lab, axis=(0, 1))
        source_std = np.std(source_lab, axis=(0, 1))
        target_mean = np.mean(target_lab, axis=(0, 1))
        target_std = np.std(target_lab, axis=(0, 1))

        # 色彩迁移
        result_lab = source_lab.copy()
        for i in range(3):
            result_lab[:, :, i] = (source_lab[:, :, i] - source_mean[i]) * (target_std[i] / source_std[i]) + target_mean[i]

        # 限制范围
        result_lab[:, :, 0] = np.clip(result_lab[:, :, 0], 0, 100)
        result_lab[:, :, 1] = np.clip(result_lab[:, :, 1], -127, 127)
        result_lab[:, :, 2] = np.clip(result_lab[:, :, 2], -127, 127)

        # 转换回RGB
        result_rgb = cv2.cvtColor(result_lab.astype(np.uint8), cv2.COLOR_LAB2RGB)
        result_tensor = torch.from_numpy(result_rgb).float() / 255.0

        return result_tensor.permute(2, 0, 1).to(source.device)

    def _skin_smoothing(self, img: torch.Tensor, strength: float) -> torch.Tensor:
        """皮肤平滑处理"""
        # 使用双边滤波进行皮肤平滑
        smoothed = self._bilateral_filter(img, d=15, sigma_color=80, sigma_space=80)

        # 创建皮肤掩码（简化版本，实际可以使用更复杂的皮肤检测）
        skin_mask = self._create_skin_mask(img)

        # 应用平滑
        result = img * (1 - skin_mask * strength) + smoothed * (skin_mask * strength)

        return result

    def _create_skin_mask(self, img: torch.Tensor) -> torch.Tensor:
        """创建皮肤掩码"""
        # 简化的皮肤检测，基于HSV色彩空间
        img_np = (img.permute(1, 2, 0).cpu().numpy() * 255).astype(np.uint8)
        hsv = cv2.cvtColor(img_np, cv2.COLOR_RGB2HSV)

        # 皮肤色彩范围
        lower_skin = np.array([0, 20, 70], dtype=np.uint8)
        upper_skin = np.array([20, 255, 255], dtype=np.uint8)

        mask = cv2.inRange(hsv, lower_skin, upper_skin)

        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (11, 11))
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)

        # 高斯模糊软化边缘
        mask = cv2.GaussianBlur(mask, (15, 15), 0)

        mask_tensor = torch.from_numpy(mask).float() / 255.0
        return mask_tensor.unsqueeze(0).to(img.device)

    def _detail_enhancement(self, img: torch.Tensor) -> torch.Tensor:
        """细节增强"""
        # 使用拉普拉斯算子增强细节
        kernel = self._laplacian_kernels['sharpen'].to(img.device)

        enhanced_channels = []
        for i in range(img.shape[0]):
            channel = img[i:i+1].unsqueeze(0)
            enhanced = F.conv2d(channel, kernel, padding=1)
            enhanced_channels.append(enhanced.squeeze(0))

        enhanced = torch.cat(enhanced_channels, dim=0)

        # 混合原图和增强结果
        alpha = 0.3
        result = (1 - alpha) * img + alpha * enhanced

        return torch.clamp(result, 0, 1)

    def _edge_optimization(self, swapped: torch.Tensor, original: torch.Tensor) -> torch.Tensor:
        """边缘优化"""
        # 检测边缘
        swapped_gray = torch.mean(swapped, dim=0, keepdim=True)
        original_gray = torch.mean(original, dim=0, keepdim=True)

        # 使用Sobel算子检测边缘
        sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]],
                              dtype=torch.float32).unsqueeze(0).unsqueeze(0).to(swapped.device)
        sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]],
                              dtype=torch.float32).unsqueeze(0).unsqueeze(0).to(swapped.device)

        edge_x = F.conv2d(swapped_gray.unsqueeze(0), sobel_x, padding=1)
        edge_y = F.conv2d(swapped_gray.unsqueeze(0), sobel_y, padding=1)
        edge_magnitude = torch.sqrt(edge_x**2 + edge_y**2).squeeze(0)

        # 边缘掩码
        edge_mask = torch.sigmoid((edge_magnitude - 0.1) * 10)

        # 在边缘区域应用更强的混合
        result = swapped * (1 - edge_mask * 0.3) + original * (edge_mask * 0.3)

        return result

    def _intelligent_blending(self, original_img: torch.Tensor,
                            enhanced_face: torch.Tensor,
                            transform_matrix: np.ndarray,
                            kps_5: np.ndarray,
                            parameters: Dict[str, Any]) -> torch.Tensor:
        """智能混合算法"""
        # 1. 反向变换增强的人脸
        inv_transform = cv2.invertAffineTransform(transform_matrix)

        enhanced_np = enhanced_face.permute(1, 2, 0).cpu().numpy()
        warped_face = cv2.warpAffine(
            enhanced_np, inv_transform,
            (original_img.shape[2], original_img.shape[1]),
            flags=cv2.INTER_LANCZOS4
        )

        warped_face_tensor = torch.from_numpy(warped_face).permute(2, 0, 1).to(original_img.device)

        # 2. 创建精确的人脸掩码
        face_mask = self._create_precise_face_mask(original_img, kps_5)

        # 3. 根据混合方法进行融合
        if self.config.blend_method == "poisson":
            result = self._poisson_blending(original_img, warped_face_tensor, face_mask)
        elif self.config.blend_method == "seamless":
            result = self._seamless_blending(original_img, warped_face_tensor, face_mask)
        else:
            result = self._alpha_blending(original_img, warped_face_tensor, face_mask)

        return result

    def _create_precise_face_mask(self, img: torch.Tensor, kps_5: np.ndarray) -> torch.Tensor:
        """创建精确的人脸掩码"""
        h, w = img.shape[1], img.shape[2]
        mask = np.zeros((h, w), dtype=np.uint8)

        # 基于关键点创建椭圆掩码
        center_x = np.mean(kps_5[:, 0])
        center_y = np.mean(kps_5[:, 1])

        # 计算椭圆参数
        width = np.max(kps_5[:, 0]) - np.min(kps_5[:, 0])
        height = np.max(kps_5[:, 1]) - np.min(kps_5[:, 1])

        # 扩展掩码以包含更多面部区域
        width = int(width * 1.4)
        height = int(height * 1.6)

        cv2.ellipse(mask, (int(center_x), int(center_y)),
                   (width//2, height//2), 0, 0, 360, 255, -1)

        # 羽化边缘
        mask = cv2.GaussianBlur(mask, (self.config.feather_amount*2+1,
                                      self.config.feather_amount*2+1), 0)

        mask_tensor = torch.from_numpy(mask).float() / 255.0
        return mask_tensor.unsqueeze(0).to(img.device)

    def _poisson_blending(self, background: torch.Tensor,
                         foreground: torch.Tensor,
                         mask: torch.Tensor) -> torch.Tensor:
        """泊松混合"""
        # 简化的泊松混合实现
        # 实际应用中可以使用OpenCV的seamlessClone

        bg_np = (background.permute(1, 2, 0).cpu().numpy() * 255).astype(np.uint8)
        fg_np = (foreground.permute(1, 2, 0).cpu().numpy() * 255).astype(np.uint8)
        mask_np = (mask.squeeze(0).cpu().numpy() * 255).astype(np.uint8)

        # 找到掩码中心
        moments = cv2.moments(mask_np)
        if moments['m00'] != 0:
            center_x = int(moments['m10'] / moments['m00'])
            center_y = int(moments['m01'] / moments['m00'])
        else:
            center_x, center_y = mask_np.shape[1]//2, mask_np.shape[0]//2

        try:
            result = cv2.seamlessClone(fg_np, bg_np, mask_np,
                                     (center_x, center_y), cv2.NORMAL_CLONE)
        except:
            # 如果泊松混合失败，回退到alpha混合
            result = self._alpha_blending_np(bg_np, fg_np, mask_np)

        result_tensor = torch.from_numpy(result).float() / 255.0
        return result_tensor.permute(2, 0, 1).to(background.device)

    def _seamless_blending(self, background: torch.Tensor,
                          foreground: torch.Tensor,
                          mask: torch.Tensor) -> torch.Tensor:
        """无缝混合"""
        # 多层次混合
        result = background.clone()

        # 创建多个尺度的掩码
        scales = [1.0, 0.8, 0.6, 0.4]
        weights = [0.4, 0.3, 0.2, 0.1]

        for scale, weight in zip(scales, weights):
            if scale < 1.0:
                # 缩小掩码
                scaled_mask = F.interpolate(
                    mask.unsqueeze(0),
                    scale_factor=scale,
                    mode='bilinear',
                    align_corners=False
                ).squeeze(0)

                # 重新调整到原尺寸
                scaled_mask = F.interpolate(
                    scaled_mask.unsqueeze(0),
                    size=(mask.shape[1], mask.shape[2]),
                    mode='bilinear',
                    align_corners=False
                ).squeeze(0)
            else:
                scaled_mask = mask

            # 应用加权混合
            blend_strength = self.config.blend_strength * weight
            result = result * (1 - scaled_mask * blend_strength) + \
                    foreground * (scaled_mask * blend_strength)

        return result

    def _alpha_blending(self, background: torch.Tensor,
                       foreground: torch.Tensor,
                       mask: torch.Tensor) -> torch.Tensor:
        """Alpha混合"""
        alpha = mask * self.config.blend_strength
        result = background * (1 - alpha) + foreground * alpha
        return result

    def _alpha_blending_np(self, bg: np.ndarray, fg: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """NumPy版本的Alpha混合"""
        alpha = mask.astype(np.float32) / 255.0
        alpha = np.stack([alpha] * 3, axis=2)

        result = bg.astype(np.float32) * (1 - alpha) + fg.astype(np.float32) * alpha
        return result.astype(np.uint8)

# 性能优化工具函数
class SwapOptimizer:
    """换脸性能优化器"""

    @staticmethod
    def optimize_model_for_inference(model):
        """优化模型用于推理"""
        if hasattr(model, 'eval'):
            model.eval()

        # 启用推理模式
        if hasattr(torch, 'inference_mode'):
            return torch.inference_mode()(model)
        else:
            return torch.no_grad()(model)

    @staticmethod
    def enable_tensorrt_optimization(model, input_shape):
        """启用TensorRT优化"""
        try:
            import torch_tensorrt

            # 编译模型
            optimized_model = torch_tensorrt.compile(
                model,
                inputs=[torch_tensorrt.Input(input_shape)],
                enabled_precisions={torch.half}
            )
            return optimized_model
        except ImportError:
            print("TensorRT not available, using standard PyTorch")
            return model

    @staticmethod
    def batch_process_faces(faces_list, model, batch_size=4):
        """批量处理人脸"""
        results = []

        for i in range(0, len(faces_list), batch_size):
            batch = faces_list[i:i+batch_size]
            batch_tensor = torch.stack(batch)

            with torch.no_grad():
                batch_result = model(batch_tensor)

            results.extend([result for result in batch_result])

        return results
