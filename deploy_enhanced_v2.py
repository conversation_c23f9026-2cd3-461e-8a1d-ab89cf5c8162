#!/usr/bin/env python3
"""
部署增强换脸功能 V2.0
安全的架构，避免递归调用
"""

import sys
import os
import json
import datetime
import time

def setup_environment():
    """设置环境变量"""
    os.environ['ROPE_SESSION'] = 'enhanced_v2_' + str(int(datetime.datetime.now().timestamp()))
    os.environ['ROPE_LAUNCH_TIME'] = datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")

def test_enhanced_modules():
    """测试增强模块"""
    try:
        print("测试增强模块...")
        
        # 测试基础模块
        from rope.enhanced_face_swap_v2 import EnhancedFaceSwapperV2, EnhancedSwapConfigV2
        print("✓ EnhancedFaceSwapperV2")
        
        from rope.safe_swap_integration import SafeSwapIntegration, integrate_safe_enhanced_swap
        print("✓ SafeSwapIntegration")
        
        # 测试配置
        config = EnhancedSwapConfigV2()
        print(f"✓ 默认配置加载成功 - 质量模式: {config.quality_mode}")
        
        # 测试增强器初始化
        enhancer = EnhancedFaceSwapperV2(config)
        print("✓ 增强器初始化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 增强模块测试失败: {e}")
        return False

def test_video_manager_integration():
    """测试VideoManager集成"""
    try:
        print("测试VideoManager集成...")
        
        from rope.VideoManager import VideoManager
        print("✓ VideoManager导入成功")
        
        # 检查是否有增强功能相关方法
        vm = VideoManager(None, None, None, None)
        
        if hasattr(vm, 'enhanced_swap_integration'):
            print("✓ 增强换脸集成属性存在")
        
        if hasattr(vm, 'get_enhanced_swap_stats'):
            print("✓ 统计方法已添加")
        
        if hasattr(vm, 'update_enhanced_swap_config'):
            print("✓ 配置更新方法已添加")
        
        return True
        
    except Exception as e:
        print(f"❌ VideoManager集成测试失败: {e}")
        return False

def create_quality_presets():
    """创建质量预设"""
    try:
        print("创建质量预设...")
        
        presets = {
            "fast": {
                "name": "快速模式",
                "description": "最快速度，基础质量",
                "UseEnhancedSwap": False,
                "QualityMode": "fast"
            },
            "normal": {
                "name": "标准模式", 
                "description": "平衡速度和质量",
                "UseEnhancedSwap": True,
                "QualityMode": "normal",
                "enable_face_enhancement": True,
                "enable_skin_smoothing": True,
                "enable_color_matching": True,
                "blend_method": "alpha",
                "blend_strength": 0.85,
                "feather_amount": 12
            },
            "high": {
                "name": "高质量模式",
                "description": "更好的质量，稍慢速度",
                "UseEnhancedSwap": True,
                "QualityMode": "high",
                "enable_face_enhancement": True,
                "enable_skin_smoothing": True,
                "enable_color_matching": True,
                "blend_method": "poisson",
                "blend_strength": 0.9,
                "feather_amount": 15,
                "skin_smooth_strength": 0.3
            },
            "ultra": {
                "name": "极致质量模式",
                "description": "最佳质量，较慢速度",
                "UseEnhancedSwap": True,
                "QualityMode": "ultra",
                "enable_face_enhancement": True,
                "enable_skin_smoothing": True,
                "enable_color_matching": True,
                "enable_super_resolution": True,
                "blend_method": "seamless",
                "blend_strength": 0.95,
                "feather_amount": 20,
                "skin_smooth_strength": 0.35,
                "upscale_factor": 2.0
            }
        }
        
        os.makedirs("config", exist_ok=True)
        with open("config/quality_presets.json", 'w', encoding='utf-8') as f:
            json.dump(presets, f, indent=2, ensure_ascii=False)
        
        print("✓ 质量预设已创建")
        return True
        
    except Exception as e:
        print(f"❌ 创建质量预设失败: {e}")
        return False

def create_user_guide():
    """创建用户指南"""
    try:
        print("创建用户指南...")
        
        guide = """
# 增强换脸功能 V2.0 用户指南

## 🎯 功能特点

### 质量模式
- **快速模式**: 最快速度，使用原始算法
- **标准模式**: 平衡速度和质量，启用基础增强
- **高质量模式**: 更好的质量，使用泊松混合
- **极致质量模式**: 最佳质量，包含超分辨率

### 增强功能
1. **人脸质量增强**: 自适应直方图均衡化 + 锐化
2. **皮肤平滑**: 智能皮肤检测 + 双边滤波
3. **色彩匹配**: 直方图匹配 / LAB色彩迁移
4. **智能混合**: Alpha / 泊松 / 无缝混合

## 🚀 使用方法

### 基础使用
1. 启动程序
2. 选择摄像头或视频
3. 加载源人脸图片
4. 选择质量模式（在设置中）
5. 启用换脸功能

### 高级设置
编辑 `config/enhanced_swap_v2.json`:

```json
{
  "UseEnhancedSwap": true,
  "QualityMode": "normal",
  "blend_method": "alpha",
  "blend_strength": 0.85,
  "enable_color_matching": true
}
```

## ⚙️ 配置参数

### 基础设置
- `UseEnhancedSwap`: 启用增强功能
- `QualityMode`: 质量模式 (fast/normal/high/ultra)

### 混合设置
- `blend_method`: 混合方法 (alpha/poisson/seamless)
- `blend_strength`: 混合强度 (0.0-1.0)
- `feather_amount`: 羽化程度 (0-30)

### 增强设置
- `enable_face_enhancement`: 启用人脸增强
- `enable_skin_smoothing`: 启用皮肤平滑
- `enable_color_matching`: 启用色彩匹配

## 🔧 故障排除

### 常见问题
1. **增强功能不工作**: 检查 `UseEnhancedSwap` 设置
2. **处理速度慢**: 降低质量模式或关闭超分辨率
3. **效果不自然**: 调整混合强度和羽化参数
4. **色彩不匹配**: 尝试不同的色彩迁移方法

### 性能优化
- 使用 `fast` 模式获得最快速度
- 关闭不需要的增强功能
- 降低目标分辨率
- 启用GPU优化

## 📊 统计信息

程序会自动收集性能统计:
- 总处理次数
- 增强处理次数
- 平均处理时间
- 增强成功率

可通过控制台查看详细统计信息。
"""
        
        with open("增强换脸功能指南.md", 'w', encoding='utf-8') as f:
            f.write(guide)
        
        print("✓ 用户指南已创建")
        return True
        
    except Exception as e:
        print(f"❌ 创建用户指南失败: {e}")
        return False

def verify_deployment():
    """验证部署"""
    try:
        print("验证部署...")
        
        # 检查文件
        required_files = [
            "rope/enhanced_face_swap_v2.py",
            "rope/safe_swap_integration.py", 
            "config/enhanced_swap_v2.json",
            "config/quality_presets.json"
        ]
        
        for file_path in required_files:
            if os.path.exists(file_path):
                print(f"✓ {file_path}")
            else:
                print(f"❌ {file_path} 缺失")
                return False
        
        # 测试导入
        try:
            from rope.enhanced_face_swap_v2 import EnhancedFaceSwapperV2
            from rope.safe_swap_integration import SafeSwapIntegration
            print("✓ 模块导入成功")
        except ImportError as e:
            print(f"❌ 模块导入失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 验证部署失败: {e}")
        return False

def show_deployment_summary():
    """显示部署总结"""
    print("\n" + "="*60)
    print("🎉 增强换脸功能 V2.0 部署完成")
    print("="*60)
    
    print("\n📋 新功能特点:")
    print("  ✓ 避免递归调用的安全架构")
    print("  ✓ 多种质量模式 (快速/标准/高质量/极致)")
    print("  ✓ 智能人脸增强和皮肤平滑")
    print("  ✓ 高级色彩匹配算法")
    print("  ✓ 多种混合方法 (Alpha/泊松/无缝)")
    print("  ✓ 性能监控和统计")
    print("  ✓ 可配置的质量预设")
    
    print("\n⚙️ 配置文件:")
    print("  • config/enhanced_swap_v2.json - 主配置")
    print("  • config/quality_presets.json - 质量预设")
    
    print("\n🚀 使用方法:")
    print("  1. 重启程序")
    print("  2. 增强功能会自动启用")
    print("  3. 在设置中选择质量模式")
    print("  4. 享受更好的换脸效果")
    
    print("\n📊 性能监控:")
    print("  • 实时处理统计")
    print("  • 增强成功率监控")
    print("  • 自动性能优化")
    
    print("\n🔧 故障排除:")
    print("  • 查看控制台日志")
    print("  • 调整质量模式")
    print("  • 参考用户指南")

def main():
    """主函数"""
    print("=" * 60)
    print("Rope Live Stellar - 部署增强换脸功能 V2.0")
    print("=" * 60)
    
    # 设置环境
    setup_environment()
    
    tasks = [
        ("测试增强模块", test_enhanced_modules),
        ("测试VideoManager集成", test_video_manager_integration),
        ("创建质量预设", create_quality_presets),
        ("创建用户指南", create_user_guide),
        ("验证部署", verify_deployment),
    ]
    
    completed = 0
    total = len(tasks)
    
    for task_name, task_func in tasks:
        print(f"\n--- {task_name} ---")
        try:
            if task_func():
                completed += 1
                print(f"✅ {task_name} 完成")
            else:
                print(f"⚠️ {task_name} 部分完成")
        except Exception as e:
            print(f"❌ {task_name} 失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"部署结果: {completed}/{total} 完成")
    
    if completed >= 4:  # 至少完成主要任务
        print("🎉 增强换脸功能 V2.0 部署成功！")
        show_deployment_summary()
        
        print("\n🚀 下一步:")
        print("  1. 重启 Rope Live Stellar")
        print("  2. 测试增强换脸功能")
        print("  3. 根据需要调整质量模式")
        
        return True
    else:
        print("⚠️ 部分部署失败，请检查错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
