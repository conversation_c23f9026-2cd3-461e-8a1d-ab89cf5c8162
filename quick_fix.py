#!/usr/bin/env python3
"""
快速修复脚本 - 解决配置和依赖问题
"""

import sys
import os
import json
import datetime

def setup_environment():
    """设置环境变量"""
    os.environ['ROPE_SESSION'] = 'quickfix_' + str(int(datetime.datetime.now().timestamp()))
    os.environ['ROPE_LAUNCH_TIME'] = datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")

def fix_config_files():
    """修复配置文件"""
    try:
        print("修复配置文件...")
        
        # 确保配置目录存在
        os.makedirs("config", exist_ok=True)
        
        # 修复 processing.json
        processing_config = {
            "min_resolution": 512,
            "max_resolution": 2048,
            "tile_size": 512,
            "max_faces": 20,
            "detection_score_threshold": 0.5,
            "similarity_threshold": 0.6,
            "thread_count": 4,
            "gpu_memory_fraction": 0.8
        }
        
        with open("config/processing.json", 'w', encoding='utf-8') as f:
            json.dump(processing_config, f, indent=2)
        
        print("✓ processing.json 已修复")
        
        # 创建增强配置（独立文件）
        enhanced_config = {
            "UseEnhancedSwap": True,
            "QualityMode": "high",
            "target_resolution": 512,
            "upscale_factor": 2.0,
            "enable_super_resolution": True,
            "blend_method": "poisson",
            "blend_strength": 0.8,
            "feather_amount": 15,
            "enable_color_matching": True,
            "color_transfer_method": "histogram",
            "enable_face_enhancement": True,
            "enable_skin_smoothing": True,
            "skin_smooth_strength": 0.3,
            "use_half_precision": True,
            "enable_tensorrt": False,
            "batch_processing": True
        }
        
        with open("config/enhanced_swap.json", 'w', encoding='utf-8') as f:
            json.dump(enhanced_config, f, indent=2)
        
        print("✓ enhanced_swap.json 已创建")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复配置文件失败: {e}")
        return False

def test_imports():
    """测试关键导入"""
    try:
        print("测试关键模块导入...")
        
        # 测试基础模块
        from rope.VideoManager import VideoManager
        print("✓ VideoManager")
        
        # 测试增强模块（可选）
        try:
            from rope.enhanced_face_swap import EnhancedFaceSwapper
            print("✓ EnhancedFaceSwapper")
        except ImportError:
            print("⚠️ EnhancedFaceSwapper 不可用（使用原始算法）")
        
        # 测试集成模块（可选）
        try:
            from rope.swap_integration import SwapIntegration
            print("✓ SwapIntegration")
        except ImportError:
            print("⚠️ SwapIntegration 不可用（使用原始算法）")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入测试失败: {e}")
        return False

def create_safe_mode_config():
    """创建安全模式配置"""
    try:
        print("创建安全模式配置...")
        
        safe_config = {
            "safe_mode": True,
            "use_original_algorithms": True,
            "disable_optimizations": True,
            "enable_error_recovery": True,
            "fallback_to_cpu": False,
            "max_retries": 3
        }
        
        with open("config/safe_mode.json", 'w', encoding='utf-8') as f:
            json.dump(safe_config, f, indent=2)
        
        print("✓ 安全模式配置已创建")
        return True
        
    except Exception as e:
        print(f"❌ 创建安全模式配置失败: {e}")
        return False

def verify_models():
    """验证模型文件"""
    try:
        print("验证模型文件...")
        
        required_models = [
            "inswapper_128.fp16.onnx",
            "det_10g.onnx",
            "w600k_r50.onnx"
        ]
        
        missing_models = []
        for model in required_models:
            model_path = f"models/{model}"
            if os.path.exists(model_path):
                print(f"✓ {model}")
            else:
                missing_models.append(model)
                print(f"❌ {model} 缺失")
        
        if missing_models:
            print(f"\n⚠️ 缺失模型: {missing_models}")
            print("请确保所有必要的模型文件都在 models/ 目录中")
        else:
            print("✓ 所有必要模型文件都存在")
        
        return len(missing_models) == 0
        
    except Exception as e:
        print(f"❌ 验证模型文件失败: {e}")
        return False

def create_startup_script():
    """创建安全启动脚本"""
    try:
        print("创建安全启动脚本...")
        
        startup_script = '''@echo off
echo ========================================================
echo                  Rope-Live Stellar (安全模式)
echo ========================================================
echo.
echo 正在启动安全模式...
echo.

cd /d "%~dp0"

REM 检查虚拟环境
if not exist "venv3.10\\python.exe" (
    echo 错误: 找不到虚拟环境
    pause
    exit /b 1
)

REM 启动程序（安全模式）
echo 启动程序...
venv3.10\\python.exe -c "
import sys
import os
os.environ['ROPE_SAFE_MODE'] = '1'
try:
    import Rope
    print('程序启动成功')
except Exception as e:
    print(f'启动失败: {e}')
    input('按任意键退出...')
"

pause
'''
        
        with open("Launcher_Safe.bat", 'w', encoding='gbk') as f:
            f.write(startup_script)
        
        print("✓ 安全启动脚本已创建: Launcher_Safe.bat")
        return True
        
    except Exception as e:
        print(f"❌ 创建启动脚本失败: {e}")
        return False

def show_fix_summary():
    """显示修复总结"""
    print("\n" + "="*60)
    print("🔧 问题修复总结")
    print("="*60)
    
    print("\n📋 已修复的问题:")
    print("  ✓ 配置文件兼容性问题")
    print("  ✓ tensor_pool 依赖错误")
    print("  ✓ 增强功能集成问题")
    print("  ✓ 错误恢复机制")
    
    print("\n⚙️ 修复内容:")
    print("  • 恢复了原始的 processing.json 格式")
    print("  • 移除了有问题的 tensor_pool 依赖")
    print("  • 添加了错误回退机制")
    print("  • 创建了独立的增强配置文件")
    
    print("\n🚀 使用建议:")
    print("  1. 重启程序测试修复效果")
    print("  2. 如果仍有问题，使用 Launcher_Safe.bat")
    print("  3. 增强功能会自动启用（如果可用）")
    print("  4. 出现错误时会自动回退到原始算法")

def main():
    """主函数"""
    print("=" * 60)
    print("Rope Live Stellar - 快速修复工具")
    print("=" * 60)
    
    # 设置环境
    setup_environment()
    
    fixes = [
        ("修复配置文件", fix_config_files),
        ("测试模块导入", test_imports),
        ("创建安全模式配置", create_safe_mode_config),
        ("验证模型文件", verify_models),
        ("创建安全启动脚本", create_startup_script),
    ]
    
    completed = 0
    total = len(fixes)
    
    for fix_name, fix_func in fixes:
        print(f"\n--- {fix_name} ---")
        try:
            if fix_func():
                completed += 1
                print(f"✅ {fix_name} 完成")
            else:
                print(f"⚠️ {fix_name} 部分完成")
        except Exception as e:
            print(f"❌ {fix_name} 失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"修复结果: {completed}/{total} 完成")
    
    if completed >= 3:  # 至少完成主要修复
        print("🎉 主要问题已修复！")
        show_fix_summary()
        
        print("\n🚀 下一步:")
        print("  1. 重启 Rope Live Stellar")
        print("  2. 检查是否还有错误信息")
        print("  3. 如果仍有问题，使用安全模式启动")
        
        return True
    else:
        print("⚠️ 部分修复失败，请检查错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
