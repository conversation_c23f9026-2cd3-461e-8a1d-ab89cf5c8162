#!/usr/bin/env python3
"""
深度诊断质量模式无效果问题
"""

import sys
import os
import datetime
import json

def setup_environment():
    """设置环境变量"""
    os.environ['ROPE_SESSION'] = 'debug_quality_' + str(int(datetime.datetime.now().timestamp()))
    os.environ['ROPE_LAUNCH_TIME'] = datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")

def check_integration_status():
    """检查集成状态"""
    try:
        print("检查增强功能集成状态...")
        
        # 检查VideoManager是否正确集成了增强功能
        from rope.VideoManager import VideoManager
        
        # 检查类定义中是否有增强相关代码
        import inspect
        vm_source = inspect.getsource(VideoManager.__init__)
        
        if "safe_swap_integration" in vm_source:
            print("✓ VideoManager包含安全集成代码")
        else:
            print("❌ VideoManager缺少安全集成代码")
            return False
        
        if "enhanced_swap_integration" in vm_source:
            print("✓ VideoManager包含增强集成属性")
        else:
            print("❌ VideoManager缺少增强集成属性")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查集成状态失败: {e}")
        return False

def check_swap_core_replacement():
    """检查swap_core是否被正确替换"""
    try:
        print("检查swap_core替换状态...")
        
        # 创建VideoManager实例来检查
        from rope.VideoManager import VideoManager
        
        # 模拟创建VideoManager（简化参数）
        print("  - 尝试创建VideoManager实例...")
        
        # 检查是否有增强相关方法
        vm_methods = [method for method in dir(VideoManager) if not method.startswith('_')]
        enhanced_methods = [m for m in vm_methods if 'enhanced' in m.lower()]
        
        print(f"  - VideoManager方法总数: {len(vm_methods)}")
        print(f"  - 增强相关方法: {enhanced_methods}")
        
        # 检查swap_core方法
        if hasattr(VideoManager, 'swap_core'):
            print("✓ VideoManager有swap_core方法")
            
            # 检查方法源码
            import inspect
            try:
                swap_core_source = inspect.getsource(VideoManager.swap_core)
                if "enhanced" in swap_core_source.lower():
                    print("✓ swap_core包含增强相关代码")
                else:
                    print("⚠️ swap_core不包含增强相关代码")
            except:
                print("⚠️ 无法获取swap_core源码")
        else:
            print("❌ VideoManager缺少swap_core方法")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查swap_core替换失败: {e}")
        return False

def test_enhancement_pipeline():
    """测试增强处理管道"""
    try:
        print("测试增强处理管道...")
        
        from rope.enhanced_face_swap_v2 import EnhancedFaceSwapperV2, EnhancedSwapConfigV2
        import numpy as np
        
        # 创建测试数据
        test_img = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
        test_landmarks = np.array([[200, 200], [300, 200], [250, 250], [220, 300], [280, 300]])
        
        print(f"  - 测试图像: {test_img.shape}")
        print(f"  - 测试关键点: {test_landmarks.shape}")
        
        # 测试不同质量模式
        modes = ['fast', 'normal', 'high', 'ultra']
        
        for mode in modes:
            print(f"\n  测试 {mode} 模式:")
            
            config = EnhancedSwapConfigV2()
            config.quality_mode = mode
            config.enabled = True
            
            processor = EnhancedFaceSwapperV2(config)
            
            # 检查是否应该使用增强
            should_enhance = processor.should_use_enhancement(test_img.shape, mode)
            print(f"    - 应该增强: {should_enhance}")
            
            if should_enhance:
                try:
                    # 创建一个稍微不同的图像作为swapped_face
                    swapped_face = test_img.copy()
                    swapped_face[:, :, 0] = np.clip(swapped_face[:, :, 0] + 20, 0, 255)  # 稍微改变红色通道
                    
                    result = processor.process_face_swap(
                        target_img=test_img,
                        swapped_face=swapped_face,
                        face_landmarks=test_landmarks,
                        quality_mode=mode
                    )
                    
                    # 检查结果是否有变化
                    diff = np.mean(np.abs(result.astype(float) - swapped_face.astype(float)))
                    print(f"    - 处理成功，平均差异: {diff:.2f}")
                    
                    if diff > 1.0:
                        print(f"    ✓ {mode}模式有明显效果")
                    else:
                        print(f"    ⚠️ {mode}模式效果不明显")
                        
                except Exception as e:
                    print(f"    ❌ {mode}模式处理失败: {e}")
            else:
                print(f"    - {mode}模式跳过增强（预期行为）")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试增强处理管道失败: {e}")
        return False

def create_visible_effect_config():
    """创建明显效果的配置"""
    try:
        print("创建明显效果配置...")
        
        # 创建一个效果更明显的配置
        visible_config = {
            "UseEnhancedSwap": True,
            "QualityMode": "normal",
            
            # 增强混合效果
            "blend_method": "alpha",
            "blend_strength": 0.95,  # 增强混合强度
            "feather_amount": 25,    # 增加羽化
            
            # 增强色彩匹配
            "enable_color_matching": True,
            "color_transfer_method": "lab",  # 使用LAB色彩迁移
            
            # 增强人脸处理
            "enable_face_enhancement": True,
            "enable_skin_smoothing": True,
            "skin_smooth_strength": 0.6,  # 增强皮肤平滑
            
            # 质量模式特定设置
            "quality_modes": {
                "fast": {
                    "UseEnhancedSwap": False
                },
                "normal": {
                    "UseEnhancedSwap": True,
                    "blend_strength": 0.8,
                    "skin_smooth_strength": 0.3
                },
                "high": {
                    "UseEnhancedSwap": True,
                    "blend_method": "poisson",
                    "blend_strength": 0.9,
                    "skin_smooth_strength": 0.5,
                    "feather_amount": 20
                },
                "ultra": {
                    "UseEnhancedSwap": True,
                    "blend_method": "seamless",
                    "blend_strength": 0.95,
                    "skin_smooth_strength": 0.6,
                    "feather_amount": 25,
                    "enable_super_resolution": True
                }
            },
            
            # 调试设置
            "enable_debug_logging": True,
            "show_processing_info": True,
            "enable_visual_debug": True
        }
        
        os.makedirs("config", exist_ok=True)
        with open("config/visible_effect.json", 'w', encoding='utf-8') as f:
            json.dump(visible_config, f, indent=2)
        
        print("✓ 明显效果配置已创建: config/visible_effect.json")
        return True
        
    except Exception as e:
        print(f"❌ 创建明显效果配置失败: {e}")
        return False

def add_debug_logging():
    """添加调试日志"""
    try:
        print("添加调试日志...")
        
        # 修改SafeSwapIntegration以添加更多日志
        integration_file = "rope/safe_swap_integration.py"
        
        # 读取文件
        with open(integration_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有调试日志
        if "self.logger.info(f\"质量模式切换到: {mapped_mode}\")" not in content:
            # 在质量模式映射后添加日志
            old_pattern = 'mapped_mode = quality_mode_mapping.get(quality_mode, \'normal\')'
            new_pattern = '''mapped_mode = quality_mode_mapping.get(quality_mode, 'normal')
                    
                    # 调试日志
                    self.logger.info(f"质量模式切换到: {mapped_mode} (原始: {quality_mode})")
                    self.logger.info(f"增强功能启用: {self.config.enabled}")
                    self.logger.info(f"应该增强: {self._should_enhance(parameters, control)}")'''
            
            content = content.replace(old_pattern, new_pattern)
            
            # 写回文件
            with open(integration_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✓ 调试日志已添加到SafeSwapIntegration")
        else:
            print("✓ 调试日志已存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 添加调试日志失败: {e}")
        return False

def create_test_instructions():
    """创建测试说明"""
    try:
        print("创建测试说明...")
        
        instructions = """
# 质量模式效果测试说明

## 🎯 问题诊断

当前问题：质量模式切换没有视觉效果差异

## 🔍 可能原因

### 1. 增强功能未真正启用
- 集成代码存在但未实际工作
- swap_core方法未被正确替换
- 参数传递有问题

### 2. 效果不够明显
- 增强参数设置过于保守
- 源图像质量影响效果
- 测试条件不合适

## 🧪 详细测试步骤

### 步骤1: 检查控制台日志
重启程序，观察控制台输出：

**应该看到**：
```
✓ 安全增强换脸功能已集成
增强换脸处理器V2初始化完成 - 质量模式: normal
```

**切换质量模式时应该看到**：
```
质量模式切换到: high (原始: 高质量)
增强功能启用: True
应该增强: True
```

### 步骤2: 使用明显效果配置
1. 复制 config/visible_effect.json 到 config/enhanced_swap_v2.json
2. 重启程序
3. 测试质量模式切换

### 步骤3: 测试不同场景
1. **使用高质量源图片**：清晰、正面、光照良好
2. **测试不同分辨率**：尝试不同的摄像头分辨率
3. **对比明显差异**：快速 vs 极致模式

### 步骤4: 验证增强功能
1. 关闭"启用增强换脸"开关
2. 观察是否有差异
3. 重新开启开关
4. 再次测试

## 🎮 测试技巧

### 寻找明显差异
- **边缘融合**：观察人脸边缘是否更平滑
- **色彩匹配**：注意肤色是否更自然
- **皮肤质感**：检查皮肤是否更细腻
- **整体自然度**：评估整体效果

### 最佳测试条件
- 使用高质量源人脸图片
- 确保良好的光照条件
- 选择合适的摄像头分辨率
- 在"标准"和"极致"之间切换对比

## 🔧 故障排除

### 如果仍然没有效果
1. **检查日志**：确认增强功能真正启用
2. **重新集成**：重启程序重新加载集成
3. **使用调试配置**：应用visible_effect.json配置
4. **测试不同图片**：尝试多种源人脸图片

### 强制启用效果
编辑 config/enhanced_swap_v2.json：
```json
{
  "UseEnhancedSwap": true,
  "blend_strength": 0.95,
  "skin_smooth_strength": 0.6,
  "feather_amount": 25,
  "enable_color_matching": true,
  "enable_face_enhancement": true,
  "enable_skin_smoothing": true
}
```

## 📊 预期结果

### 正常工作时
- 控制台显示质量模式切换日志
- 快速模式 vs 极致模式有明显差异
- 边缘更平滑，色彩更自然

### 如果仍无效果
- 可能需要重新检查集成代码
- 或者增强算法参数需要调整
- 建议提供详细的控制台日志进行进一步诊断
"""
        
        with open("质量模式效果测试说明.md", 'w', encoding='utf-8') as f:
            f.write(instructions)
        
        print("✓ 测试说明已创建")
        return True
        
    except Exception as e:
        print(f"❌ 创建测试说明失败: {e}")
        return False

def show_diagnosis_summary():
    """显示诊断总结"""
    print("\n" + "="*60)
    print("🔍 质量模式无效果问题诊断总结")
    print("="*60)
    
    print("\n📋 可能的根本原因:")
    print("  1. 增强功能集成了但未真正工作")
    print("  2. swap_core方法未被正确替换")
    print("  3. 增强效果参数设置过于保守")
    print("  4. 测试条件不合适")
    
    print("\n🔧 已创建的调试工具:")
    print("  • config/visible_effect.json - 明显效果配置")
    print("  • 质量模式效果测试说明.md - 详细测试指南")
    print("  • 增强的调试日志")
    
    print("\n🎯 下一步诊断:")
    print("  1. 重启程序，观察控制台日志")
    print("  2. 切换质量模式，查看是否有日志输出")
    print("  3. 如果没有日志，说明增强功能未真正启用")
    print("  4. 如果有日志但无效果，需要调整参数")
    
    print("\n🚀 立即测试:")
    print("  1. 重启程序")
    print("  2. 观察控制台是否有'质量模式切换到'日志")
    print("  3. 如果没有，说明集成有问题")
    print("  4. 如果有，尝试使用visible_effect.json配置")

def main():
    """主函数"""
    print("=" * 60)
    print("Rope Live Stellar - 深度诊断质量模式无效果")
    print("=" * 60)
    
    # 设置环境
    setup_environment()
    
    diagnostics = [
        ("检查集成状态", check_integration_status),
        ("检查swap_core替换", check_swap_core_replacement),
        ("测试增强处理管道", test_enhancement_pipeline),
        ("创建明显效果配置", create_visible_effect_config),
        ("添加调试日志", add_debug_logging),
        ("创建测试说明", create_test_instructions),
    ]
    
    completed = 0
    total = len(diagnostics)
    
    for diag_name, diag_func in diagnostics:
        print(f"\n--- {diag_name} ---")
        try:
            if diag_func():
                completed += 1
                print(f"✅ {diag_name} 完成")
            else:
                print(f"⚠️ {diag_name} 发现问题")
        except Exception as e:
            print(f"❌ {diag_name} 失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"诊断结果: {completed}/{total} 正常")
    
    show_diagnosis_summary()
    
    return completed >= 4

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
