2025-06-12 14:53:13,282 - FaceEnhancement - WARNING - enhanced_face_swap_v2.py:164 - apply_skin_smoothing - 皮肤平滑失败: operands could not be broadcast together with shapes (480,640,3) (480,640) 
2025-06-12 14:53:13,341 - FaceEnhancement - WARNING - enhanced_face_swap_v2.py:164 - apply_skin_smoothing - 皮肤平滑失败: operands could not be broadcast together with shapes (480,640,3) (480,640) 
2025-06-12 15:14:53,746 - FaceEnhancement - WARNING - enhanced_face_swap_v2.py:136 - enhance_image_quality - 图像质量增强失败: OpenCV(4.10.0) d:\a\opencv-python\opencv-python\opencv\modules\imgproc\src\color.simd_helpers.hpp:94: error: (-2:Unspecified error) in function '__cdecl cv::impl::`anonymous-namespace'::CvtHelper<struct cv::impl::`anonymous namespace'::Set<3,4,-1>,struct cv::impl::A0x0dfba5aa::Set<3,-1,-1>,struct cv::impl::A0x0dfba5aa::Set<0,5,-1>,4>::CvtHelper(const class cv::_InputArray &,const class cv::_OutputArray &,int)'
> Unsupported depth of input image:
>     'VDepth::contains(depth)'
> where
>     'depth' is 4 (CV_32S)

2025-06-12 15:14:53,748 - FaceEnhancement - WARNING - enhanced_face_swap_v2.py:167 - apply_skin_smoothing - 皮肤平滑失败: OpenCV(4.10.0) D:\a\opencv-python\opencv-python\opencv\modules\imgproc\src\bilateral_filter.dispatch.cpp:429: error: (-210:Unsupported format or combination of formats) Bilateral filtering is only implemented for 8u and 32f images in function 'cv::bilateralFilter'

