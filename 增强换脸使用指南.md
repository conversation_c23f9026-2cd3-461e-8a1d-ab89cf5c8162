# Rope Live Stellar 增强换脸功能使用指南

## 🚀 新功能概述

增强换脸功能为Rope Live Stellar带来了显著的质量提升：

### 📈 质量改进
- **高质量人脸对齐**: 使用更精确的关键点对齐算法
- **多尺度处理**: 支持超分辨率处理，提升细节表现
- **智能色彩匹配**: 自动匹配源脸和目标脸的色彩
- **泊松混合**: 使用泊松编辑实现无缝融合
- **皮肤平滑**: 智能皮肤平滑，保持自然效果
- **细节增强**: 增强面部细节，提升清晰度
- **边缘优化**: 优化换脸边缘，减少违和感

### ⚙️ 配置选项

#### 质量设置
- `target_resolution`: 目标分辨率 (512/1024)
- `upscale_factor`: 上采样倍数 (1.5-3.0)
- `enable_super_resolution`: 启用超分辨率

#### 混合算法
- `blend_method`: 混合方法
  - `poisson`: 泊松混合 (推荐)
  - `alpha`: Alpha混合 (快速)
  - `seamless`: 无缝混合 (高质量)
- `blend_strength`: 混合强度 (0.0-1.0)
- `feather_amount`: 羽化程度 (5-30)

#### 后处理
- `enable_color_matching`: 启用色彩匹配
- `enable_face_enhancement`: 启用人脸增强
- `enable_skin_smoothing`: 启用皮肤平滑
- `skin_smooth_strength`: 皮肤平滑强度 (0.0-1.0)

### 🎯 质量模式

程序会根据以下条件自动选择处理模式：

1. **Ultra模式** (最高质量)
   - 高端GPU (RTX 3080+)
   - 充足的GPU内存 (>8GB)
   - 非实时处理

2. **High模式** (高质量)
   - 中高端GPU (RTX 2070+)
   - 适中的GPU内存 (>6GB)
   - 实时处理可接受

3. **Normal模式** (标准质量)
   - 中端GPU (GTX 1660+)
   - 基础GPU内存 (>4GB)
   - 实时处理优先

4. **Fast模式** (快速模式)
   - 低端GPU或CPU处理
   - 内存不足时
   - 追求速度时

### 💡 使用建议

#### 硬件配置建议
- **最低配置**: GTX 1660 6GB + 16GB RAM
- **推荐配置**: RTX 3070 8GB + 32GB RAM
- **最佳配置**: RTX 4080 16GB + 64GB RAM

#### 设置建议
1. **首次使用**: 使用默认设置，质量模式设为 "high"
2. **性能不足**: 降低 `target_resolution` 到 256 或 384
3. **追求质量**: 启用 `enable_super_resolution`，设置 `upscale_factor` 为 2.0
4. **实时处理**: 使用 "normal" 或 "fast" 模式

#### 故障排除
- **内存不足**: 降低 `target_resolution` 或关闭 `enable_super_resolution`
- **处理太慢**: 使用 "fast" 模式或降低质量设置
- **效果不佳**: 调整 `blend_method` 或增加 `feather_amount`

### 📁 配置文件

- `config/processing.json`: 主要处理配置
- `config/enhanced_swap.json`: 增强换脸专用配置
- `config/gui_enhanced.json`: GUI增强选项

### 🔧 高级设置

对于高级用户，可以直接编辑配置文件来精细调整参数：

```json
{
  "target_resolution": 512,
  "upscale_factor": 2.0,
  "blend_method": "poisson",
  "blend_strength": 0.8,
  "feather_amount": 15,
  "enable_color_matching": true,
  "enable_face_enhancement": true,
  "enable_skin_smoothing": true,
  "skin_smooth_strength": 0.3
}
```

### 🎉 享受更好的换脸效果！

增强换脸功能将显著提升您的换脸体验，带来更自然、更高质量的结果。
