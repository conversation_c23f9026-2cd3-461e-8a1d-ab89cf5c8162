"""
统一错误处理系统
"""
import traceback
import sys
from enum import Enum
from typing import Optional, Callable, Dict, Any, Union
from dataclasses import dataclass
from datetime import datetime
import threading
from pathlib import Path
import json

from .logger import get_logger

class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """错误类别"""
    SYSTEM = "system"
    NETWORK = "network"
    GPU = "gpu"
    MODEL = "model"
    UI = "ui"
    LICENSE = "license"
    FILE_IO = "file_io"
    UNKNOWN = "unknown"

@dataclass
class ErrorInfo:
    """错误信息"""
    error: Exception
    severity: ErrorSeverity
    category: ErrorCategory
    context: str
    timestamp: datetime
    traceback_str: str
    recovery_attempted: bool = False
    recovery_successful: bool = False

class ErrorHandler:
    """统一错误处理器"""
    
    def __init__(self):
        self.logger = get_logger("ErrorHandler")
        self.error_history: list[ErrorInfo] = []
        self.recovery_strategies: Dict[ErrorCategory, Callable] = {}
        self.error_callbacks: Dict[ErrorSeverity, list[Callable]] = {
            severity: [] for severity in ErrorSeverity
        }
        self._lock = threading.Lock()
        
        # 注册默认恢复策略
        self._register_default_recovery_strategies()
    
    def _register_default_recovery_strategies(self):
        """注册默认恢复策略"""
        self.recovery_strategies[ErrorCategory.GPU] = self._recover_gpu_error
        self.recovery_strategies[ErrorCategory.MODEL] = self._recover_model_error
        self.recovery_strategies[ErrorCategory.NETWORK] = self._recover_network_error
        self.recovery_strategies[ErrorCategory.FILE_IO] = self._recover_file_io_error
    
    def register_recovery_strategy(self, category: ErrorCategory, strategy: Callable):
        """注册恢复策略"""
        self.recovery_strategies[category] = strategy
    
    def register_error_callback(self, severity: ErrorSeverity, callback: Callable):
        """注册错误回调"""
        self.error_callbacks[severity].append(callback)
    
    def handle_error(self, 
                    error: Exception, 
                    severity: ErrorSeverity, 
                    category: ErrorCategory = ErrorCategory.UNKNOWN,
                    context: str = "",
                    attempt_recovery: bool = True) -> bool:
        """
        处理错误
        
        Args:
            error: 异常对象
            severity: 错误严重程度
            category: 错误类别
            context: 错误上下文
            attempt_recovery: 是否尝试恢复
            
        Returns:
            bool: 是否应该继续执行程序
        """
        with self._lock:
            # 创建错误信息
            error_info = ErrorInfo(
                error=error,
                severity=severity,
                category=category,
                context=context,
                timestamp=datetime.now(),
                traceback_str=traceback.format_exc()
            )
            
            # 记录错误
            self._log_error(error_info)
            
            # 尝试恢复
            if attempt_recovery and category in self.recovery_strategies:
                try:
                    error_info.recovery_attempted = True
                    recovery_func = self.recovery_strategies[category]
                    error_info.recovery_successful = recovery_func(error, context)
                    
                    if error_info.recovery_successful:
                        self.logger.info(f"错误恢复成功: {context}")
                    else:
                        self.logger.warning(f"错误恢复失败: {context}")
                        
                except Exception as recovery_error:
                    self.logger.error(f"恢复策略执行失败: {recovery_error}")
                    error_info.recovery_successful = False
            
            # 添加到历史记录
            self.error_history.append(error_info)
            
            # 执行回调
            for callback in self.error_callbacks[severity]:
                try:
                    callback(error_info)
                except Exception as callback_error:
                    self.logger.error(f"错误回调执行失败: {callback_error}")
            
            # 决定是否继续执行
            should_continue = self._should_continue_execution(error_info)
            
            # 保存错误报告
            if severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
                self._save_error_report(error_info)
            
            return should_continue
    
    def _log_error(self, error_info: ErrorInfo):
        """记录错误日志"""
        log_message = f"[{error_info.category.value.upper()}] {error_info.context}: {str(error_info.error)}"
        
        if error_info.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message, exc_info=True)
        elif error_info.severity == ErrorSeverity.HIGH:
            self.logger.error(log_message, exc_info=True)
        elif error_info.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)
    
    def _should_continue_execution(self, error_info: ErrorInfo) -> bool:
        """判断是否应该继续执行程序"""
        if error_info.severity == ErrorSeverity.CRITICAL:
            return False
        
        if error_info.severity == ErrorSeverity.HIGH:
            return error_info.recovery_successful
        
        return True
    
    def _save_error_report(self, error_info: ErrorInfo):
        """保存错误报告"""
        try:
            reports_dir = Path("logs/error_reports")
            reports_dir.mkdir(parents=True, exist_ok=True)
            
            report_file = reports_dir / f"error_{error_info.timestamp.strftime('%Y%m%d_%H%M%S')}.json"
            
            report_data = {
                "timestamp": error_info.timestamp.isoformat(),
                "severity": error_info.severity.value,
                "category": error_info.category.value,
                "context": error_info.context,
                "error_type": type(error_info.error).__name__,
                "error_message": str(error_info.error),
                "traceback": error_info.traceback_str,
                "recovery_attempted": error_info.recovery_attempted,
                "recovery_successful": error_info.recovery_successful
            }
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"保存错误报告失败: {e}")
    
    # 默认恢复策略
    def _recover_gpu_error(self, error: Exception, context: str) -> bool:
        """GPU错误恢复策略"""
        try:
            import torch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                return True
        except Exception:
            pass
        return False
    
    def _recover_model_error(self, error: Exception, context: str) -> bool:
        """模型错误恢复策略"""
        # 可以实现模型重新加载等策略
        return False
    
    def _recover_network_error(self, error: Exception, context: str) -> bool:
        """网络错误恢复策略"""
        # 可以实现重试机制
        return False
    
    def _recover_file_io_error(self, error: Exception, context: str) -> bool:
        """文件IO错误恢复策略"""
        # 可以实现文件路径修复等策略
        return False
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        with self._lock:
            total_errors = len(self.error_history)
            if total_errors == 0:
                return {"total_errors": 0}
            
            severity_counts = {}
            category_counts = {}
            recovery_success_rate = 0
            
            for error_info in self.error_history:
                # 统计严重程度
                severity = error_info.severity.value
                severity_counts[severity] = severity_counts.get(severity, 0) + 1
                
                # 统计类别
                category = error_info.category.value
                category_counts[category] = category_counts.get(category, 0) + 1
                
                # 统计恢复成功率
                if error_info.recovery_attempted and error_info.recovery_successful:
                    recovery_success_rate += 1
            
            recovery_attempts = sum(1 for e in self.error_history if e.recovery_attempted)
            recovery_success_rate = (recovery_success_rate / recovery_attempts * 100) if recovery_attempts > 0 else 0
            
            return {
                "total_errors": total_errors,
                "severity_distribution": severity_counts,
                "category_distribution": category_counts,
                "recovery_success_rate": f"{recovery_success_rate:.1f}%",
                "recovery_attempts": recovery_attempts
            }

# 全局错误处理器
error_handler = ErrorHandler()

# 便捷函数
def handle_error(error: Exception, 
                severity: ErrorSeverity, 
                category: ErrorCategory = ErrorCategory.UNKNOWN,
                context: str = "",
                attempt_recovery: bool = True) -> bool:
    """处理错误的便捷函数"""
    return error_handler.handle_error(error, severity, category, context, attempt_recovery)

# 装饰器用于自动错误处理
def auto_error_handler(severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                      category: ErrorCategory = ErrorCategory.UNKNOWN,
                      context: str = ""):
    """自动错误处理装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                func_context = context or f"{func.__module__}.{func.__name__}"
                should_continue = handle_error(e, severity, category, func_context)
                if not should_continue:
                    raise
                return None
        return wrapper
    return decorator
