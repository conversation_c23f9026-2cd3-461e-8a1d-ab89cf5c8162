#!/usr/bin/env python3

import warnings
warnings.filterwarnings("ignore", category=FutureWarning)
import atexit
import tkinter as tk
from tkinter import ttk, messagebox
import sys
from rope import Coordinator
from rope.license_utils import validate_activation_code, get_machine_code, check_license
from datetime import datetime
import os
from pathlib import Path
from rope.language_manager import load_language_dict  
import json
import random
import subprocess
import win32api
import win32con
import cv2
from rope.config import SOFTWARE_VERSION

# 加载语言字典
Dicts = load_language_dict()
DEFAULT_DATA = Dicts.DEFAULT_DATA


def verify_launch():
    """验证是否通过启动器启动"""
    session_key = os.environ.get('ROPE_SESSION')
    launch_time = os.environ.get('ROPE_LAUNCH_TIME')
    
    if not session_key or not launch_time:
        messagebox.showerror(
            DEFAULT_DATA['LaunchError'],
            DEFAULT_DATA['LaunchErrorMsg']
        )
        sys.exit(1)
    
    try:
        
        # 尝试多种时间格式
        time_formats = [
            '%Y/%m/%d %H:%M:%S',
            '%Y-%m-%d %H:%M:%S',
            '%d/%m/%Y %H:%M:%S',
            '%Y/%m/%d %H:%M:%S',
            '%m/%d/%Y %H:%M:%S'
        ]
        
        launch_datetime = None
        for time_format in time_formats:
            try:
                launch_datetime = datetime.strptime(launch_time, time_format)
                break
            except ValueError:
                continue
                
        if not launch_datetime:
            raise ValueError(f"{DEFAULT_DATA['Error']}: {launch_time}")
            
        time_diff = (datetime.now() - launch_datetime).total_seconds()
        
        if time_diff > 600:  # 启动超时
            messagebox.showerror(
                DEFAULT_DATA['Error'],
                DEFAULT_DATA['LaunchTimeout']
            )
            sys.exit(1)
            
    except Exception as e:
        messagebox.showerror(DEFAULT_DATA['LaunchError'], str(e))
        sys.exit(1)

def cleanup():
    try:
        tk.Tk().destroy()
    except:
        pass

atexit.register(cleanup)


def initialize_license_system():
    BASE_DIR = Path(__file__).resolve().parent
    TOOLS_DIR = BASE_DIR / 'tools'
    LICENSE_ENC_PATH = TOOLS_DIR / 'license.enc'

    os.makedirs(TOOLS_DIR, exist_ok=True)
    
    # 验证许可证，无论是否存在license.enc文件
    try:
        license_valid = check_license(SOFTWARE_VERSION)  # 传入版本标识
        return license_valid
    except Exception as e:
        return False

def show_activation_dialog(root):
    try:
        dialog = tk.Toplevel(root)
        dialog.title(DEFAULT_DATA['ActivationTitle'])
        dialog.geometry("300x220")
        dialog.resizable(False, False)
        dialog.configure(bg='#F0F0E0')

        # 设置关闭窗口时退出程序
        dialog.protocol("WM_DELETE_WINDOW", lambda: sys.exit())

        try:
            style = ttk.Style()
            style.theme_use('clam')
            
            # 配置按钮样式
            style.configure('TButton', 
                            font=('Arial', 10),
                            borderwidth=1,
                            relief="raised",
                            background="#f0f0f0")
            
            # 配置标签样式
            style.configure('TLabel', 
                            font=('Arial', 10),
                            background="#f0f0f0")
            
            # 配置输入框样式
            style.configure('TEntry', 
                            font=('Arial', 10),
                            fieldbackground="#ffffff")
            
            # 配置框架样式
            style.configure('TFrame', background="#f0f0f0")
            
        except Exception as e:
            messagebox.showwarning("Style Warning", f"Error configuring style: {e}")

        main_frame = ttk.Frame(dialog, padding="20 20 20 20", style='TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 保存对图像的引用
        dialog.image_refs = []  # 防止图像被垃圾回收

        def activate():
            try:
                activation_code = activation_code_entry.get().strip()
                success, response = validate_activation_code(activation_code, SOFTWARE_VERSION)  # 传入版本标识
                
                if success and isinstance(response, dict):
                    validity_period = response.get('validity_period')
                    
                    # 转换有效期显示文本
                    period_display = {
                        'permanent': DEFAULT_DATA['Permanent'],
                        '1day': DEFAULT_DATA['1day'],
                        '3days': DEFAULT_DATA['3days'], 
                        '1week': DEFAULT_DATA['1week'], 
                        '1month': DEFAULT_DATA['1month'],
                        '3months': DEFAULT_DATA['3months'],
                        '6months': DEFAULT_DATA['6months'],
                        '1year': DEFAULT_DATA['1year']
                    }.get(validity_period, validity_period)
                    
                    # 显示成功消息
                    messagebox.showinfo(
                        DEFAULT_DATA['ActivationSuccess'],
                        DEFAULT_DATA['ActivationSuccessMsg'].format(period_display)
                    )
                    
                    # 关闭激活窗口和root窗口
                    dialog.destroy()
                    
                    # 设置环境变量
                    timestamp = datetime.now().strftime("%H%M%S")
                    session_key = f"{random.randint(1000,9999)}{random.randint(1000,9999)}{timestamp}"
                    launch_time = datetime.now().strftime("%Y/%m/%d %H:%M:%S")
                    
                    os.environ['ROPE_SESSION'] = session_key
                    os.environ['ROPE_LAUNCH_TIME'] = launch_time
                    
                    # 不再重新启动程序，而是返回到main函数继续执行
                    return
                    
                else:
                    error_message = response if isinstance(response, str) else DEFAULT_DATA['LicenseActivationError']
                    messagebox.showerror(
                        DEFAULT_DATA['ActivationError'],
                        error_message
                    )
                    
            except Exception as e:
                messagebox.showerror(
                    DEFAULT_DATA['Error'],
                    DEFAULT_DATA['InvalidActivationCode']
                )

        ttk.Label(main_frame, text=DEFAULT_DATA['ActivationCodeLabel']).pack(anchor=tk.W, pady=(0, 5))
        activation_code_entry = ttk.Entry(main_frame, width=40)
        activation_code_entry.pack(fill=tk.X, pady=(0, 10))

        activate_button = ttk.Button(main_frame, text=DEFAULT_DATA['ActivateButton'], command=activate)
        activate_button.pack(fill=tk.X, pady=(0, 10))

        machine_code_label = ttk.Label(
            main_frame, 
            text=DEFAULT_DATA['BuyActivationCodeLabel'],
            wraplength=260, 
            foreground='red'
        )
        machine_code_label.pack(fill=tk.X, pady=(5, 0), side=tk.BOTTOM)

        def buy_activation_code():
            # 打开购买激活码页面
            import webbrowser
            if SOFTWARE_VERSION == "starpluck":
                webbrowser.open(DEFAULT_DATA['TelegramUrl'])
            elif SOFTWARE_VERSION == "stellar":
                webbrowser.open(DEFAULT_DATA['BuyActivationCodeLink'])

        buy_activation_code_button = ttk.Button(
            main_frame, 
            text=DEFAULT_DATA['BuyActivationCode'],
            command=buy_activation_code
        )
        buy_activation_code_button.pack(fill=tk.X)

        dialog.update_idletasks()
        dialog.geometry("+{}+{}".format(
            (dialog.winfo_screenwidth() - dialog.winfo_width()) // 2,
            (dialog.winfo_screenheight() - dialog.winfo_height()) // 2
        ))

        dialog.grab_set()
        dialog.wait_window()  # 使用wait_window代替mainloop，等待窗口关闭


    except Exception as e:
        messagebox.showerror(
            DEFAULT_DATA['Error'],
            DEFAULT_DATA['CreateActivationWindowError'].format(str(e))
        )
        sys.exit(1)

    # 如果对话框被关闭而不是通过激活按钮退出，则退出程序
    if not check_license(SOFTWARE_VERSION):  # 传入版本标识
        sys.exit()

def print_startup_info():
    """打印启动信息"""
    # 不需要打印任何信息
    pass

def update_obs_config():
    try:
        # 获取 OBS Websocket 配置文件路径
        obs_config_path = os.path.join(os.getenv('APPDATA'), 'obs-studio', 'plugin_config', 'obs-websocket', 'config.json')
        
        # 读取 OBS Websocket 配置
        with open(obs_config_path, 'r', encoding='utf-8') as f:
            obs_config = json.load(f)

        #结束OBS进程，隐藏输出日志    
        subprocess.run(['taskkill', '/F', '/IM', 'obs64.exe'], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        #更新配置项中的  "server_enabled": false, 改为 "server_enabled": true,
        obs_config['server_enabled'] = True
        #写入配置文件
        with open(obs_config_path, 'w', encoding='utf-8') as f:
            json.dump(obs_config, f, indent=4)

        # 获取需要的配置项
        server_password = obs_config.get('server_password', '')
        server_port = obs_config.get('server_port', 4455)
        
        # 获取我们的配置文件路径
        if getattr(sys, 'frozen', False):
            exe_path = os.path.abspath(sys.executable)
            base_path = os.path.dirname(exe_path)
            config_path = os.path.join(base_path, 'config', 'config.json')
        else:
            config_path = Path(__file__).resolve().parent / 'config' / 'config.json'
            
        # 确保配置目录存在
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        # 读取现有配置（如果存在）
        config_data = {}
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
        
        # 更新配置
        config_data['obs'] = config_data.get('obs', {})
        config_data['obs'].update({
            'host': '127.0.0.1',
            'port': server_port,
            'password': server_password
        })
        
        # 写入配置文件
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=4, ensure_ascii=False)
        
        return True
        
    except Exception as e:
        messagebox.showerror("配置更新错误", f"更新配置文件时出错: {str(e)}")
        return False

def uninstall_droidcam():
    try:
        uninstall_path = r"C:\Program Files\DroidCam\Drivers\uninstall.exe"
        if os.path.exists(uninstall_path):
            # 使用列表形式传递命令和参数
            subprocess.Popen([uninstall_path, "/S"], 
                           shell=False,  # 改为 False，因为我们使用列表形式
                           creationflags=subprocess.CREATE_NO_WINDOW | subprocess.DETACHED_PROCESS)  # 添加 DETACHED_PROCESS
    except Exception as e:
        pass

# 注册清理函数
atexit.register(uninstall_droidcam)

def delete_droidcam_plugin():
    """删除DroidCam插件"""
    try:
        # 使用Python方式获取OBS路径
        obs_path = ""
        
        # 从注册表获取
        try:
            import winreg
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\OBS Studio") as key:
                obs_path, _ = winreg.QueryValueEx(key, "")
        except Exception:
            pass
            
        # 如果注册表查找失败，检查常见安装路径
        if not obs_path:
            for drive in "CDEFGH":
                path = f"{drive}:\\Program Files\\obs-studio"
                if os.path.exists(path):
                    obs_path = path
                    break
        
        if obs_path:
            #关闭OBS进程
            subprocess.run(['taskkill', '/F', '/IM', 'obs64.exe'], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            # 删除插件文件
            plugin_dll = os.path.join(obs_path, "obs-plugins", "64bit", "droidcam-virtual-output.dll")
            if os.path.exists(plugin_dll):
                os.remove(plugin_dll)
                
            # 删除插件数据文件夹
            plugin_data = os.path.join(obs_path, "data", "obs-plugins", "droidcam-virtual-output")
            if os.path.exists(plugin_data):
                import shutil
                shutil.rmtree(plugin_data)
                
            return True
        return False
    except Exception:
        # 在退出事件处理程序中不应弹出消息框
        return False

def perform_cleanup():
    """执行所有清理操作"""
    
    # 1. 关闭OBS进程
    try:
        subprocess.run(['taskkill', '/F', '/IM', 'obs64.exe'], 
                      stdout=subprocess.DEVNULL, 
                      stderr=subprocess.DEVNULL,
                      timeout=5)
    except Exception as e:
        pass
    
    # 2. 删除OBS插件
    try:
        # 获取OBS路径
        obs_path = ""
        try:
            import winreg
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\OBS Studio") as key:
                obs_path, _ = winreg.QueryValueEx(key, "")
        except Exception:
            pass
            
        if not obs_path:
            for drive in "CDEFGH":
                path = f"{drive}:\\Program Files\\obs-studio"
                if os.path.exists(path):
                    obs_path = path
                    break
        
        if obs_path:
            # 删除插件文件
            plugin_dll = os.path.join(obs_path, "obs-plugins", "64bit", "droidcam-virtual-output.dll")
            if os.path.exists(plugin_dll):
                os.remove(plugin_dll)
                
            # 删除插件数据文件夹
            plugin_data = os.path.join(obs_path, "data", "obs-plugins", "droidcam-virtual-output")
            if os.path.exists(plugin_data):
                import shutil
                shutil.rmtree(plugin_data)
        else:
            pass
    except Exception as e:
        pass
    
    # 3. 卸载DroidCam驱动
    try:
        uninstall_path = r"C:\Program Files\DroidCam\Drivers\uninstall.exe"
        if os.path.exists(uninstall_path):
            subprocess.Popen([uninstall_path, "/S"], 
                           shell=True,
                           creationflags=subprocess.CREATE_NO_WINDOW)
    except Exception as e:
        pass
    

def handle_console_exit(ctrl_type):
    """处理控制台关闭事件"""
    if ctrl_type in (win32con.CTRL_C_EVENT, win32con.CTRL_BREAK_EVENT, win32con.CTRL_CLOSE_EVENT):
        perform_cleanup()
    return False

# 注册事件处理函数
win32api.SetConsoleCtrlHandler(handle_console_exit, True)

# 同时注册atexit函数作为备份
import atexit
atexit.register(perform_cleanup)

def install_droidcam_driver():
    """安装DroidCam驱动"""
    # 安装驱动文件
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe
        exe_path = os.path.abspath(sys.executable)
        base_path = os.path.dirname(exe_path)
        local_installer_path = os.path.join(base_path,  'ext_dependencies', 'wireshark', 'DroidCam.Drivers.7.1.1.exe')
    else:
        # 如果是开发环境
        local_installer_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ext_dependencies', 'wireshark', 'DroidCam.Drivers.7.1.1.exe')
    
    if os.path.exists(local_installer_path):
        try:
            subprocess.run([local_installer_path, '/S'], check=True)
        except Exception as e:
            messagebox.showerror("驱动安装错误", f"驱动安装失败: {str(e)}")
    else:
        messagebox.showerror("驱动安装错误", f"驱动安装程序不存在: {local_installer_path}")

def install_droidcam_plugin():
    """安装DroidCam插件"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe
        exe_path = os.path.abspath(sys.executable)
        base_path = os.path.dirname(exe_path)
        local_installer_path = os.path.join(base_path,  'ext_dependencies', 'wireshark', 'OBS.Plugin.0.2.1.exe')
    else:
        # 如果是开发环境
        local_installer_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ext_dependencies', 'wireshark', 'OBS.Plugin.0.2.1.exe')
    
    if os.path.exists(local_installer_path):
        try:
            subprocess.run([local_installer_path, '/S'], check=True)
        except Exception as e:
            messagebox.showerror("插件安装错误", f"插件安装失败: {str(e)}")
    else:
        messagebox.showerror("插件安装错误", f"插件安装程序不存在: {local_installer_path}")

def limit_camera_resolution():
    """限制摄像头分辨率，优化性能"""
    try:
        # 尝试打开默认摄像头
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            # 获取摄像头名称
            camera_name = cap.getBackendName()
            
            # 检查是否是罗技C1000e
            if "C1000e" in camera_name or "Logitech" in camera_name:
                # 设置较低的分辨率
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1920)  # 1080p
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 1080)
                # 设置帧率
                cap.set(cv2.CAP_PROP_FPS, 30)
            
            # 释放摄像头
            cap.release()
    except Exception as e:
        pass

def main():
    """优化的主函数，包含完整的错误处理和资源管理"""
    from rope.logger import get_logger
    from rope.error_handler import handle_error, ErrorSeverity, ErrorCategory
    from rope.resource_manager import resource_manager
    from rope.config import config_manager

    logger = get_logger("Main")
    logger.info("Rope Live Stellar 启动中...")

    try:
        # 验证启动
        verify_launch()

        # 初始化许可证系统
        license_valid = initialize_license_system()

        # 更新OBS配置
        if not update_obs_config():
            logger.warning("OBS配置更新失败，但程序将继续运行")

        # 限制摄像头分辨率
        limit_camera_resolution()

        # 注册清理函数
        resource_manager.register_cleanup(perform_cleanup, priority=100)

        # 检查许可证
        if not license_valid:
            logger.info("需要激活许可证")
            if not _handle_license_activation():
                logger.info("用户取消激活，程序退出")
                sys.exit(0)
        else:
            logger.info("许可证验证成功")
            # 安装必要的驱动和插件
            _install_dependencies()

        # 启动主程序
        logger.info("启动协调器...")
        _run_coordinator()

    except KeyboardInterrupt:
        logger.info("用户中断程序")
        sys.exit(0)
    except Exception as e:
        if not handle_error(e, ErrorSeverity.CRITICAL, ErrorCategory.SYSTEM, "主程序启动失败"):
            logger.critical("程序启动失败，即将退出")
            sys.exit(1)

def _handle_license_activation() -> bool:
    """处理许可证激活"""
    from rope.logger import get_logger
    from rope.error_handler import handle_error, ErrorSeverity, ErrorCategory

    logger = get_logger("License")

    try:
        root = tk.Tk()
        root.withdraw()

        show_activation_dialog(root)

        # 检查激活结果
        if check_license(SOFTWARE_VERSION):
            logger.info("许可证激活成功")
            root.destroy()
            return True
        else:
            logger.warning("许可证激活失败")
            root.destroy()
            return False

    except Exception as e:
        handle_error(e, ErrorSeverity.HIGH, ErrorCategory.LICENSE, "许可证激活过程出错")
        return False

def _install_dependencies():
    """安装必要的依赖"""
    from rope.logger import get_logger
    from rope.error_handler import handle_error, ErrorSeverity, ErrorCategory

    logger = get_logger("Dependencies")

    try:
        logger.info("安装DroidCam驱动...")
        install_droidcam_driver()

        logger.info("安装DroidCam插件...")
        install_droidcam_plugin()

    except Exception as e:
        handle_error(e, ErrorSeverity.MEDIUM, ErrorCategory.SYSTEM, "依赖安装失败")

def _run_coordinator():
    """运行协调器"""
    from rope.logger import get_logger
    from rope.error_handler import handle_error, ErrorSeverity, ErrorCategory

    logger = get_logger("Coordinator")

    try:
        Coordinator.run()

    except tk.TclError as e:
        error_msg = str(e)
        if "pyimage" in error_msg.lower():
            handle_error(e, ErrorSeverity.HIGH, ErrorCategory.UI, "图像资源加载失败")
            messagebox.showerror("资源错误", DEFAULT_DATA['ImageResourceLoadFailed'])
        else:
            handle_error(e, ErrorSeverity.HIGH, ErrorCategory.UI, "Tkinter错误")
            messagebox.showerror("Tkinter错误", DEFAULT_DATA['TkinterError'].format(error_msg))
        raise

    except Exception as e:
        handle_error(e, ErrorSeverity.CRITICAL, ErrorCategory.SYSTEM, "协调器运行失败")
        messagebox.showerror("错误", DEFAULT_DATA['ErrorRunningCoordinator'].format(str(e)))
        raise

if __name__ == "__main__":
    """程序入口点，包含完整的错误处理和清理机制"""
    from rope.logger import get_logger, get_performance_logger
    from rope.error_handler import handle_error, ErrorSeverity, ErrorCategory
    from rope.resource_manager import resource_manager

    # 设置日志
    logger = get_logger("Main")
    perf_logger = get_performance_logger()

    try:
        logger.info("=" * 50)
        logger.info("Rope Live Stellar 启动")
        logger.info("=" * 50)

        # 记录启动时间
        perf_logger.start_timer("application_startup")

        # 运行主程序
        main()

        # 记录启动完成时间
        perf_logger.end_timer("application_startup")
        logger.info("程序启动完成")

    except KeyboardInterrupt:
        logger.info("用户中断程序")

    except Exception as e:
        # 最后的错误处理
        logger.critical(f"程序发生未处理的异常: {e}", exc_info=True)

        try:
            messagebox.showerror("严重错误",
                               f"程序遇到严重错误：\n{str(e)}\n\n请查看日志文件获取详细信息。")
        except:
            # 如果连错误对话框都无法显示，至少打印到控制台
            print(f"严重错误: {e}")

        # 尝试清理Tkinter资源
        try:
            import tkinter as tk
            root = tk._default_root
            if root:
                for widget in root.winfo_children():
                    try:
                        widget.destroy()
                    except:
                        pass
        except:
            pass

        sys.exit(1)

    finally:
        # 确保资源清理
        try:
            logger.info("开始程序清理...")
            perform_cleanup()
            resource_manager.cleanup_all()
            logger.info("程序清理完成")
        except Exception as cleanup_error:
            logger.error(f"清理过程中发生错误: {cleanup_error}")

        logger.info("Rope Live Stellar 退出")
        logger.info("=" * 50)