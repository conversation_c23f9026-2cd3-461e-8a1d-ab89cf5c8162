#!/usr/bin/env python3
"""
诊断质量模式是否正常工作
"""

import sys
import os
import datetime
import json

def setup_environment():
    """设置环境变量"""
    os.environ['ROPE_SESSION'] = 'diagnose_' + str(int(datetime.datetime.now().timestamp()))
    os.environ['ROPE_LAUNCH_TIME'] = datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")

def check_enhanced_integration():
    """检查增强功能集成状态"""
    try:
        print("检查增强功能集成状态...")
        
        # 检查VideoManager是否正确集成
        from rope.VideoManager import VideoManager
        
        # 创建一个测试实例（简化参数）
        try:
            # 模拟VideoManager初始化
            print("✓ VideoManager可以导入")
            
            # 检查是否有增强相关方法
            vm_methods = dir(VideoManager)
            enhanced_methods = [m for m in vm_methods if 'enhanced' in m.lower()]
            
            if enhanced_methods:
                print(f"✓ 发现增强相关方法: {enhanced_methods}")
            else:
                print("⚠️ 未发现增强相关方法")
            
            return True
            
        except Exception as e:
            print(f"❌ VideoManager初始化失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 检查增强功能集成失败: {e}")
        return False

def check_config_files():
    """检查配置文件"""
    try:
        print("检查配置文件...")
        
        config_files = [
            "config/enhanced_swap.json",
            "config/enhanced_swap_v2.json",
            "config/quality_presets.json"
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    
                    print(f"✓ {config_file} 存在")
                    
                    # 检查关键配置
                    if 'UseEnhancedSwap' in config_data:
                        print(f"  - UseEnhancedSwap: {config_data['UseEnhancedSwap']}")
                    
                    if 'QualityMode' in config_data:
                        print(f"  - QualityMode: {config_data['QualityMode']}")
                        
                except json.JSONDecodeError:
                    print(f"❌ {config_file} 格式错误")
                except Exception as e:
                    print(f"❌ 读取 {config_file} 失败: {e}")
            else:
                print(f"⚠️ {config_file} 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查配置文件失败: {e}")
        return False

def test_quality_mode_mapping():
    """测试质量模式映射"""
    try:
        print("测试质量模式映射...")
        
        from rope.safe_swap_integration import SafeSwapIntegration
        
        # 创建模拟的VideoManager
        class MockVideoManager:
            def __init__(self):
                self.swap_core = lambda *args, **kwargs: "mock_result"
        
        vm = MockVideoManager()
        integration = SafeSwapIntegration(vm)
        
        # 测试不同质量模式
        test_cases = [
            ('快速', False, "快速模式应该禁用增强"),
            ('标准', True, "标准模式应该启用增强"),
            ('高质量', True, "高质量模式应该启用增强"),
            ('极致', True, "极致模式应该启用增强"),
            ('Fast', False, "Fast模式应该禁用增强"),
            ('Normal', True, "Normal模式应该启用增强"),
            ('High', True, "High模式应该启用增强"),
            ('Ultra', True, "Ultra模式应该启用增强"),
        ]
        
        for mode, expected, description in test_cases:
            test_params = {
                'QualityModeTextSel': mode,
                'UseEnhancedSwapSwitch': True
            }
            test_control = {'SwapFacesButton': True}
            
            result = integration._should_enhance(test_params, test_control)
            
            if result == expected:
                print(f"✓ {mode}: {result} - {description}")
            else:
                print(f"❌ {mode}: {result} (期望: {expected}) - {description}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试质量模式映射失败: {e}")
        return False

def check_enhanced_processor():
    """检查增强处理器"""
    try:
        print("检查增强处理器...")
        
        from rope.enhanced_face_swap_v2 import EnhancedFaceSwapperV2, EnhancedSwapConfigV2
        
        # 创建配置
        config = EnhancedSwapConfigV2()
        print(f"✓ 默认配置: enabled={config.enabled}, quality_mode={config.quality_mode}")
        
        # 创建处理器
        processor = EnhancedFaceSwapperV2(config)
        print("✓ 增强处理器创建成功")
        
        # 测试不同质量模式的判断
        test_shapes = [(480, 640, 3), (720, 1280, 3)]
        test_modes = ['fast', 'normal', 'high', 'ultra']
        
        for shape in test_shapes:
            for mode in test_modes:
                should_enhance = processor.should_use_enhancement(shape, mode)
                print(f"  - 分辨率{shape[:2]}, 模式{mode}: {should_enhance}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查增强处理器失败: {e}")
        return False

def create_debug_config():
    """创建调试配置"""
    try:
        print("创建调试配置...")
        
        debug_config = {
            "UseEnhancedSwap": True,
            "QualityMode": "normal",
            "enable_debug_logging": True,
            "enable_visual_debug": True,
            "show_processing_info": True,
            "blend_strength": 0.9,  # 增强混合强度以便看到差异
            "enable_color_matching": True,
            "enable_face_enhancement": True,
            "enable_skin_smoothing": True,
            "skin_smooth_strength": 0.5  # 增强平滑强度
        }
        
        os.makedirs("config", exist_ok=True)
        with open("config/debug_enhanced.json", 'w', encoding='utf-8') as f:
            json.dump(debug_config, f, indent=2)
        
        print("✓ 调试配置已创建: config/debug_enhanced.json")
        return True
        
    except Exception as e:
        print(f"❌ 创建调试配置失败: {e}")
        return False

def create_troubleshooting_guide():
    """创建故障排除指南"""
    try:
        print("创建故障排除指南...")
        
        guide = """
# 质量模式故障排除指南

## 🔍 问题诊断

### 现象：切换质量模式没有视觉效果差异

## 🎯 可能原因和解决方案

### 1. 增强功能未启用
**检查**：
- 确认"启用增强换脸"开关是开启状态
- 查看控制台是否有"安全增强换脸功能已集成"信息

**解决**：
- 重启程序
- 检查config/enhanced_swap_v2.json中UseEnhancedSwap设置

### 2. 快速模式禁用了增强功能
**说明**：
- "快速"模式会自动禁用增强功能
- 只有"标准"、"高质量"、"极致"模式才启用增强

**测试**：
- 确保测试时使用"标准"或更高模式
- 在"标准"和"极致"之间切换对比

### 3. 源图像质量影响
**原因**：
- 低质量源图像增强效果不明显
- 已经很好的图像增强空间有限

**建议**：
- 使用不同质量的源人脸图片测试
- 尝试光照条件不同的图片

### 4. 处理参数需要调整
**调整方法**：
编辑 config/debug_enhanced.json：
```json
{
  "blend_strength": 0.9,
  "skin_smooth_strength": 0.5,
  "enable_color_matching": true,
  "feather_amount": 20
}
```

## 🧪 测试步骤

### 基础测试
1. 确认"启用增强换脸"开关开启
2. 选择"标准"模式
3. 加载清晰的源人脸图片
4. 启用换脸功能
5. 切换到"极致"模式
6. 观察边缘平滑度和色彩匹配

### 高级测试
1. 使用不同光照条件的源图片
2. 测试不同分辨率的视频
3. 观察皮肤平滑效果
4. 检查色彩匹配改善

## 📊 预期效果差异

### 标准 vs 极致模式
- **边缘融合**：极致模式边缘更平滑
- **色彩匹配**：极致模式色彩更自然
- **皮肤质感**：极致模式皮肤更细腻
- **整体自然度**：极致模式更真实

### 如果仍然没有差异
1. 查看控制台错误信息
2. 检查GPU内存使用情况
3. 尝试重启程序
4. 使用debug_enhanced.json配置

## 🔧 调试命令

### 检查集成状态
```bash
.\venv3.10\python.exe diagnose_quality_mode.py
```

### 查看详细日志
启动程序时观察控制台输出：
- "安全增强换脸功能已集成"
- "增强换脸处理器V2初始化完成"
- 质量模式切换信息

## 📞 进一步支持

如果问题仍然存在：
1. 提供控制台完整日志
2. 描述具体测试步骤
3. 说明使用的源图片类型
4. 报告系统配置信息
"""
        
        with open("质量模式故障排除指南.md", 'w', encoding='utf-8') as f:
            f.write(guide)
        
        print("✓ 故障排除指南已创建")
        return True
        
    except Exception as e:
        print(f"❌ 创建故障排除指南失败: {e}")
        return False

def show_diagnosis_summary():
    """显示诊断总结"""
    print("\n" + "="*60)
    print("🔍 质量模式诊断总结")
    print("="*60)
    
    print("\n📋 可能的原因:")
    print("  1. 增强功能实际未启用")
    print("  2. 快速模式禁用了增强功能")
    print("  3. 源图像质量影响效果")
    print("  4. 增强参数需要调整")
    
    print("\n🎯 建议检查:")
    print("  ✓ 确认'启用增强换脸'开关状态")
    print("  ✓ 使用'标准'或更高模式测试")
    print("  ✓ 尝试不同质量的源图片")
    print("  ✓ 查看控制台集成信息")
    
    print("\n🔧 调试文件:")
    print("  • config/debug_enhanced.json - 调试配置")
    print("  • 质量模式故障排除指南.md - 详细指南")
    
    print("\n🚀 下一步:")
    print("  1. 检查控制台是否有增强功能集成信息")
    print("  2. 确认使用'标准'模式而非'快速'模式")
    print("  3. 尝试使用调试配置")
    print("  4. 参考故障排除指南")

def main():
    """主函数"""
    print("=" * 60)
    print("Rope Live Stellar - 质量模式诊断工具")
    print("=" * 60)
    
    # 设置环境
    setup_environment()
    
    diagnostics = [
        ("检查增强功能集成", check_enhanced_integration),
        ("检查配置文件", check_config_files),
        ("测试质量模式映射", test_quality_mode_mapping),
        ("检查增强处理器", check_enhanced_processor),
        ("创建调试配置", create_debug_config),
        ("创建故障排除指南", create_troubleshooting_guide),
    ]
    
    completed = 0
    total = len(diagnostics)
    
    for diag_name, diag_func in diagnostics:
        print(f"\n--- {diag_name} ---")
        try:
            if diag_func():
                completed += 1
                print(f"✅ {diag_name} 完成")
            else:
                print(f"⚠️ {diag_name} 发现问题")
        except Exception as e:
            print(f"❌ {diag_name} 失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"诊断结果: {completed}/{total} 正常")
    
    show_diagnosis_summary()
    
    return completed >= 4

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
